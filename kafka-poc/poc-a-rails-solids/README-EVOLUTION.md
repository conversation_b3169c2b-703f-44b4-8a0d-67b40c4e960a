# 🚀 POC Evolution: From Redis/Sidekiq to Karafka Windowing

This POC demonstrates the evolution from traditional windowing (POC A) to Kafka-native session windowing (POC B) using Karafka.

## 🎯 Architecture Evolution

### POC A: Traditional Windowing
```
WhatsApp → Rails → MessageWindow (PostgreSQL) → SilenceDetectorJob (Sidekiq) → MIA
                                                                                ↓
                                                                        WhatsApp Response
```

### POC B: Kafka Windowing
```
WhatsApp → Rails → Kafka Producer → Karafka SessionConsumer → SessionWindowManager → MIA
                                                                                      ↓
                                                                              WhatsApp Response
```

## 🔄 Key Differences

| Aspect | POC A (Traditional) | POC B (Karafka) |
|--------|-------------------|-----------------|
| **State Storage** | PostgreSQL + Redis | Kafka Compacted Topic |
| **Job Processing** | Sidekiq | Karafka Consumers |
| **Windowing Logic** | SilenceDetectorJob | SessionWindowManager |
| **Deploy Resilience** | ❌ State lost on deploy | ✅ State persists in Kafka |
| **Scalability** | Vertical + Workers | Horizontal + Partitions |
| **Dependencies** | PostgreSQL, Redis, Sidekiq | Kafka only |

## 🚀 Quick Start

### 1. Start the Complete Stack
```bash
cd kafka-poc/poc-a-rails-solids
docker-compose up -d
```

This automatically:
- ✅ Creates all Kafka topics
- ✅ Starts both windowing systems
- ✅ Enables runtime switching

### 2. Access the Interfaces

| Service | URL | Purpose |
|---------|-----|---------|
| **WhatsApp Simulator** | http://localhost:3001 | Send messages and test windowing |
| **Rails Dashboard** | http://localhost:3000 | Monitor conversations |
| **Kafka UI** | http://localhost:8081 | View Kafka topics and messages |
| **Sidekiq UI** | http://localhost:3000/sidekiq | Monitor traditional jobs |

## 🔄 Switching Between Windowing Strategies

### Option 1: Via API
```bash
# Switch to Kafka windowing (POC B)
curl -X POST http://localhost:3000/api/windowing_strategy \
  -H "Content-Type: application/json" \
  -d '{"strategy": "kafka"}'

# Switch to traditional windowing (POC A)
curl -X POST http://localhost:3000/api/windowing_strategy \
  -H "Content-Type: application/json" \
  -d '{"strategy": "traditional"}'

# Check current strategy
curl http://localhost:3000/api/windowing_strategy
```

### Option 2: Via URL Parameter
```bash
# Send message with Kafka windowing
curl -X POST http://localhost:3000/webhook/whatsapp \
  -d "phone=123456789&message=Hello&kafka_windowing=true"

# Send message with traditional windowing
curl -X POST http://localhost:3000/webhook/whatsapp \
  -d "phone=123456789&message=Hello&kafka_windowing=false"
```

## 📊 Monitoring and Observability

### Logs Comparison
Watch the logs to see the difference:

```bash
# Traditional windowing logs
docker logs poc-a-rails | grep "TRADITIONAL WINDOWING"

# Kafka windowing logs  
docker logs poc-a-rails | grep "KAFKA WINDOWING"
docker logs poc-a-karafka | grep "KARAFKA"
```

### Kafka Topics
- `whatsapp-messages` - Incoming messages
- `whatsapp-sessions-state` - Session state (compacted)
- `whatsapp-aggregated-events` - Windowed events for MIA

## 🎬 Demo Script for Video

### 1. Show Traditional Windowing
```bash
# Set traditional windowing
curl -X POST localhost:3000/api/windowing_strategy -d '{"strategy": "traditional"}'

# Send messages
curl -X POST localhost:3000/webhook/whatsapp -d "phone=demo&message=Message 1"
curl -X POST localhost:3000/webhook/whatsapp -d "phone=demo&message=Message 2"

# Watch Sidekiq process after 10 seconds
```

### 2. Switch to Kafka Windowing
```bash
# Switch to Kafka windowing
curl -X POST localhost:3000/api/windowing_strategy -d '{"strategy": "kafka"}'

# Send messages
curl -X POST localhost:3000/webhook/whatsapp -d "phone=demo2&message=Kafka Message 1"
curl -X POST localhost:3000/webhook/whatsapp -d "phone=demo2&message=Kafka Message 2"

# Watch Karafka process after 10 seconds
```

### 3. Show Deploy Resilience
```bash
# With Kafka windowing active, restart containers
docker-compose restart rails-app karafka-consumers

# Sessions persist and continue processing!
```

## 🔧 Development

### Adding New Consumers
1. Create consumer in `app/consumers/`
2. Add route in `config/karafka.rb`
3. Restart karafka service

### Topic Configuration
Topics are automatically created with optimal settings:
- **whatsapp-sessions-state**: Compacted for state persistence
- **whatsapp-messages**: Regular retention for message flow
- **whatsapp-aggregated-events**: Regular retention for MIA processing

## 🎯 Benefits Demonstrated

1. **Deploy Resilience**: Kafka state survives container restarts
2. **Horizontal Scaling**: Partition-based scaling vs worker-based
3. **Simplified Architecture**: Fewer moving parts
4. **Better Observability**: Kafka UI shows message flow
5. **Event Sourcing**: Complete audit trail in Kafka

Perfect for demonstrating modern event-driven architecture evolution!
