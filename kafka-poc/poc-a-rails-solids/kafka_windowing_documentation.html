<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windowing con Kafka States - Documentación</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #7f8c8d;
        }
        .highlight {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background-color: #d5f4e6;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 15px 0;
        }
        .info {
            background-color: #ebf3fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fef9e7;
            border-left: 4px solid #f39c12;
            padding: 15px;
            margin: 15px 0;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .code-block {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .flow-step {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            position: relative;
        }
        .flow-step::before {
            content: "→";
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
        }
        .topic {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-size: 0.9em;
        }
        .component {
            background-color: #e74c3c;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            display: inline-block;
            margin: 5px;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 15px;
            display: inline-block;
        }
        .working { background-color: #27ae60; color: white; }
        .loading { background-color: #f39c12; color: white; }
        .pending { background-color: #95a5a6; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Windowing con Kafka States</h1>
        <p><strong>Alternativa al windowing estándar (Redis + Sidekiq) usando Kafka como state store</strong></p>

        <h2>📊 Estado Actual del Sistema</h2>
        <div class="success">
            <h3>✅ Componentes Funcionando:</h3>
            <ul>
                <li><span class="component">Rails + SessionWindowManager</span> - Windowing logic</li>
                <li><span class="component">ruby-kafka Producer</span> - Publica eventos agregados</li>
                <li><span class="component">Python MIA Consumer</span> - Procesa conversaciones</li>
                <li><span class="topic">whatsapp_aggregated_events</span> - 10 eventos generados</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⏳ En Progreso:</h3>
            <ul>
                <li><span class="component">Ruby Kafka Consumer</span> - <span class="status loading">Cargando Rails</span></li>
                <li>Flujo completo end-to-end - <span class="status pending">Esperando consumer</span></li>
            </ul>
        </div>

        <h2>🔄 Flujo de Windowing</h2>
        
        <div class="flow-step">
            <strong>1. Mensaje WhatsApp</strong><br>
            Usuario envía mensaje → Rails webhook recibe
        </div>
        
        <div class="flow-step">
            <strong>2. SessionWindowManager</strong><br>
            Almacena mensaje en Kafka state store + inicia timer de 10 segundos
        </div>
        
        <div class="flow-step">
            <strong>3. Window Logic</strong><br>
            Si llega otro mensaje → reset timer | Si 10s de silencio → cierra ventana
        </div>
        
        <div class="flow-step">
            <strong>4. Aggregated Event</strong><br>
            Publica evento agregado a <span class="topic">whatsapp_aggregated_events</span>
        </div>
        
        <div class="flow-step">
            <strong>5. Ruby Consumer</strong><br>
            Procesa evento → crea Conversation → publica a <span class="topic">mia-requests</span>
        </div>
        
        <div class="flow-step">
            <strong>6. Python MIA</strong><br>
            Procesa conversación → genera respuesta → publica a <span class="topic">mia_responses</span>
        </div>
        
        <div class="flow-step" style="margin-bottom: 20px;">
            <strong>7. Response Handler</strong><br>
            Envía respuesta a WhatsApp Simulator
        </div>

        <h2>🗄️ Kafka Topics</h2>
        
        <div class="highlight">
            <h3>Input Layer:</h3>
            <span class="topic">whatsapp_messages</span> - Mensajes individuales raw
        </div>
        
        <div class="highlight">
            <h3>State Management:</h3>
            <span class="topic">whatsapp-sessions-state</span> - Estado de ventanas de sesión
        </div>
        
        <div class="highlight">
            <h3>Processing Layer:</h3>
            <span class="topic">whatsapp_aggregated_events</span> - Eventos agregados (windowed)
        </div>
        
        <div class="highlight">
            <h3>MIA Layer:</h3>
            <span class="topic">mia-requests</span> - Conversaciones para MIA<br>
            <span class="topic">mia_responses</span> - Respuestas de MIA
        </div>

        <h2>⚙️ Componentes Técnicos</h2>
        
        <div class="info">
            <h3>SessionWindowManager (Ruby)</h3>
            <ul>
                <li><strong>Función:</strong> Maneja la lógica de windowing usando Kafka como state store</li>
                <li><strong>Timer:</strong> 10 segundos de silencio para cerrar ventana</li>
                <li><strong>Producer:</strong> ruby-kafka para publicar eventos agregados</li>
                <li><strong>Estado:</strong> <span class="status working">Funcionando</span></li>
            </ul>
        </div>
        
        <div class="info">
            <h3>Ruby Kafka Consumer</h3>
            <ul>
                <li><strong>Función:</strong> Consume eventos agregados y los envía a MIA</li>
                <li><strong>Framework:</strong> ruby-kafka con Rails</li>
                <li><strong>Estado:</strong> <span class="status loading">Cargando Rails</span></li>
            </ul>
        </div>
        
        <div class="info">
            <h3>Python MIA Consumer</h3>
            <ul>
                <li><strong>Función:</strong> Procesa conversaciones y genera respuestas</li>
                <li><strong>Framework:</strong> FastAPI + aiokafka</li>
                <li><strong>Estado:</strong> <span class="status working">Funcionando</span></li>
            </ul>
        </div>

        <h2>🎯 Ventajas del Windowing con Kafka</h2>
        
        <div class="success">
            <ul>
                <li><strong>Persistencia:</strong> Estado de ventanas persiste en Kafka</li>
                <li><strong>Escalabilidad:</strong> Kafka maneja el estado distribuido</li>
                <li><strong>Confiabilidad:</strong> No se pierden mensajes si se reinicia el sistema</li>
                <li><strong>Observabilidad:</strong> Todos los eventos están en topics auditables</li>
                <li><strong>Desacoplamiento:</strong> Productores y consumidores independientes</li>
            </ul>
        </div>

        <h2>📈 Métricas Actuales</h2>
        
        <div class="code-block">
whatsapp_aggregated_events: 10 eventos generados ✅
mia-requests: 0 eventos (esperando Ruby consumer)
mia_responses: 0 eventos (esperando flujo completo)
        </div>

        <div class="info">
            <p><strong>Próximo paso:</strong> Esperar a que Ruby Kafka Consumer termine de cargar Rails para completar el flujo end-to-end.</p>
        </div>
    </div>
</body>
</html>
