#!/bin/bash
echo "🚀 Starting POC A: Rails 8 + Sidekiq + Redis"
echo ""

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found."
    exit 1
fi

# Start services
echo "📦 Starting POC A services..."
docker-compose up -d

# Wait for services to be ready
echo ""
echo "⏳ Waiting for services to be ready..."
sleep 15

# Check service status
echo ""
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "✅ POC A is starting up!"
echo ""
echo "🌐 Access URLs:"
echo "  📱 WhatsApp Simulator: http://localhost:3001"
echo "  🚀 Rails Dashboard:    http://localhost:3000"
echo "  🔧 Sidekiq Web UI:     http://localhost:3000/sidekiq"
echo "  🤖 MIA FastAPI:        http://localhost:8000/docs"
echo "  🗄️  PostgreSQL Admin:   http://localhost:8080"
echo "  📊 Redis:              localhost:6379"
echo ""
echo "📋 Useful Commands:"
echo "  View logs:     docker-compose logs -f"
echo "  Stop POC A:    docker-compose down"
echo "  Reset data:    docker-compose down -v"
echo ""
echo "🧪 Testing:"
echo "  1. Open WhatsApp Simulator: http://localhost:3001"
echo "  2. Send messages from different phones"
echo "  3. Watch the 10-second windowing in action"
echo "  4. Monitor metrics in Rails Dashboard: http://localhost:3000"
echo ""
