<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kafka State Store - Explicación Clara</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.7;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 3em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.3em;
            margin-bottom: 50px;
            font-style: italic;
        }
        h2 {
            color: #34495e;
            border-left: 6px solid #3498db;
            padding-left: 25px;
            margin-top: 50px;
            font-size: 2em;
            background: linear-gradient(90deg, #34495e, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        h3 {
            color: #2c3e50;
            font-size: 1.4em;
            margin-top: 30px;
        }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid #2196f3;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.2);
        }
        .concept-title {
            color: #1565c0;
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        .example-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
            border: 2px solid #ff9800;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(255, 152, 0, 0.2);
        }
        .example-title {
            color: #e65100;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .step-container {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border: 2px solid #9c27b0;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(156, 39, 176, 0.2);
        }
        .step-number {
            background: #9c27b0;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .advantage-card {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid #4caf50;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.2);
        }
        .advantage-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .advantage-title {
            color: #2e7d32;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 1.2em;
        }
        .comparison-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .redis-col {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
        }
        .kafka-col {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        }
        .flow-step {
            background: white;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
        .flow-step::before {
            content: "↓";
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            font-weight: bold;
        }
        .flow-step:last-child::before {
            display: none;
        }
        .highlight-text {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
            color: #2d3436;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
        .summary-box {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 40px 0;
            text-align: center;
            box-shadow: 0 10px 30px rgba(44, 62, 80, 0.3);
        }
        .summary-title {
            font-size: 1.8em;
            margin-bottom: 15px;
            color: #ecf0f1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Kafka State Store</h1>
        <p class="subtitle">Explicación Clara y Conceptual</p>

        <div class="concept-box">
            <div class="concept-title">¿Qué es un Kafka State Store?</div>
            <p style="font-size: 1.1em; text-align: center;">
                Un <strong>Kafka State Store</strong> es una forma de usar Kafka como una <span class="highlight-text">"base de datos distribuida"</span> 
                para almacenar el estado de nuestras aplicaciones. En nuestro caso, lo usamos para guardar las 
                <span class="highlight-text">sesiones de windowing</span>.
            </p>
        </div>

        <h2><span class="emoji">🔧</span>Cómo funciona en nuestro sistema</h2>

        <div class="step-container">
            <h3><span class="step-number">1</span>Concepto de "Compacted Topic"</h3>
            <ul style="font-size: 1.1em;">
                <li>Kafka tiene un tipo especial de topic llamado <strong>"compacted"</strong></li>
                <li>En lugar de guardar TODOS los mensajes, solo guarda <span class="highlight-text">el más reciente por cada key</span></li>
                <li>Es como una tabla de base de datos donde cada <strong>user_id</strong> es la clave primaria</li>
            </ul>
        </div>

        <div class="step-container">
            <h3><span class="step-number">2</span>Almacenamiento Híbrido (Memoria + Kafka)</h3>
            <ul style="font-size: 1.1em;">
                <li><strong>Memoria:</strong> Para lecturas súper rápidas (microsegundos)</li>
                <li><strong>Kafka:</strong> Para persistencia y durabilidad (sobrevive reinicios)</li>
            </ul>
        </div>

        <div class="step-container">
            <h3><span class="step-number">3</span>Operaciones básicas</h3>
            
            <div style="margin: 20px 0;">
                <h4 style="color: #2e7d32;"><span class="emoji">📝</span>PUT (Guardar/Actualizar):</h4>
                <ul>
                    <li>Cuando llega un mensaje, guardamos la sesión en memoria</li>
                    <li>Al mismo tiempo, enviamos el estado a Kafka</li>
                    <li>Si llega otro mensaje, actualizamos ambos lugares</li>
                </ul>
            </div>

            <div style="margin: 20px 0;">
                <h4 style="color: #1976d2;"><span class="emoji">👁️</span>GET (Leer):</h4>
                <ul>
                    <li>Siempre leemos de memoria (súper rápido)</li>
                    <li>No necesitamos ir a Kafka para cada consulta</li>
                </ul>
            </div>

            <div style="margin: 20px 0;">
                <h4 style="color: #d32f2f;"><span class="emoji">🗑️</span>DELETE (Eliminar):</h4>
                <ul>
                    <li>Borramos de memoria</li>
                    <li>Enviamos un "tombstone" a Kafka (mensaje con valor null)</li>
                    <li>Kafka entiende que debe eliminar esa key</li>
                </ul>
            </div>
        </div>

        <h2><span class="emoji">🎯</span>Ejemplo práctico: La Libreta Mágica</h2>

        <div class="example-box">
            <div class="example-title"><span class="emoji">📚</span>Imagínate que tienes una <strong>libreta mágica</strong> (Kafka) y una <strong>pizarra</strong> (memoria):</div>
            
            <div class="flow-step">
                <strong>1. Llega mensaje:</strong> Escribes en la pizarra Y en la libreta
            </div>
            
            <div class="flow-step">
                <strong>2. Lees estado:</strong> Solo miras la pizarra (rápido)
            </div>
            
            <div class="flow-step">
                <strong>3. Se reinicia el sistema:</strong> La pizarra se borra, pero la libreta tiene todo
            </div>
            
            <div class="flow-step">
                <strong>4. Recuperación:</strong> Lees la libreta y reconstruyes la pizarra
            </div>
        </div>

        <h2><span class="emoji">🔄</span>Flujo de una sesión completa</h2>

        <div class="example-box">
            <div class="example-title"><span class="emoji">💬</span>Ejemplo paso a paso:</div>

            <div class="flow-step">
                <strong>Usuario envía "Hola":</strong><br>
                • <strong>Memoria:</strong> {"+549112345678": {messages: ["Hola"], expires_at: "10:30:10"}}<br>
                • <strong>Kafka:</strong> Publica este estado al topic whatsapp-sessions-state
            </div>

            <div class="flow-step">
                <strong>Usuario envía "¿Cómo estás?" (5 segundos después):</strong><br>
                • <strong>Memoria:</strong> {"+549112345678": {messages: ["Hola", "¿Cómo estás?"], expires_at: "10:30:15"}}<br>
                • <strong>Kafka:</strong> Actualiza el estado (reemplaza el anterior)
            </div>

            <div class="flow-step">
                <strong>10 segundos de silencio:</strong><br>
                • Lee el estado de memoria<br>
                • Crea evento agregado con ambos mensajes<br>
                • Publica a whatsapp_aggregated_events<br>
                • Elimina de memoria Y envía tombstone a Kafka
            </div>
        </div>

        <h2><span class="emoji">🎁</span>Ventajas de este enfoque</h2>

        <div class="advantages-grid">
            <div class="advantage-card">
                <div class="advantage-icon">⚡</div>
                <div class="advantage-title">Velocidad</div>
                <div>Lecturas en memoria súper rápidas</div>
            </div>
            <div class="advantage-card">
                <div class="advantage-icon">💾</div>
                <div class="advantage-title">Durabilidad</div>
                <div>Kafka persiste todo automáticamente</div>
            </div>
            <div class="advantage-card">
                <div class="advantage-icon">📈</div>
                <div class="advantage-title">Escalabilidad</div>
                <div>Kafka maneja millones de keys</div>
            </div>
            <div class="advantage-card">
                <div class="advantage-icon">🎯</div>
                <div class="advantage-title">Consistencia</div>
                <div>Una sola fuente de verdad</div>
            </div>
            <div class="advantage-card">
                <div class="advantage-icon">🔄</div>
                <div class="advantage-title">Recuperación</div>
                <div>Automática después de fallos</div>
            </div>
            <div class="advantage-card">
                <div class="advantage-icon">🔒</div>
                <div class="advantage-title">Confiabilidad</div>
                <div>Distribuido y tolerante a fallos</div>
            </div>
        </div>

        <h2><span class="emoji">🔍</span>¿Por qué no Redis?</h2>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Característica</th>
                    <th>Redis</th>
                    <th>Kafka State Store</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Persistencia</strong></td>
                    <td class="redis-col">Memoria volátil, se pierde al reiniciar</td>
                    <td class="kafka-col">Persistente, distribuido, más robusto</td>
                </tr>
                <tr>
                    <td><strong>Escalabilidad</strong></td>
                    <td class="redis-col">Limitado por memoria de un servidor</td>
                    <td class="kafka-col">Distribuido, escala horizontalmente</td>
                </tr>
                <tr>
                    <td><strong>Durabilidad</strong></td>
                    <td class="redis-col">Requiere configuración especial</td>
                    <td class="kafka-col">Durabilidad por defecto</td>
                </tr>
                <tr>
                    <td><strong>Auditabilidad</strong></td>
                    <td class="redis-col">No mantiene historial</td>
                    <td class="kafka-col">Todos los cambios registrados</td>
                </tr>
                <tr>
                    <td><strong>Recuperación</strong></td>
                    <td class="redis-col">Manual, compleja</td>
                    <td class="kafka-col">Automática, transparente</td>
                </tr>
            </tbody>
        </table>

        <div class="summary-box">
            <div class="summary-title">📝 Resumen</div>
            <p style="font-size: 1.2em; line-height: 1.8;">
                <strong>Kafka State Store</strong> es como tener una base de datos súper rápida (memoria)
                respaldada por una súper confiable (Kafka) que maneja automáticamente la sincronización entre ambas.
            </p>
            <p style="font-size: 1.1em; margin-top: 20px; opacity: 0.9;">
                <span class="emoji">🎯</span> <strong>Resultado:</strong> Lo mejor de ambos mundos - velocidad + confiabilidad
            </p>
        </div>

        <div style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center; border: 2px solid #4caf50;">
            <h3 style="color: #2e7d32; margin-top: 0;">🚀 Estado Actual en Nuestro Sistema</h3>
            <p style="font-size: 1.1em; color: #2e7d32;">
                ✅ <strong>10 eventos agregados</strong> generados exitosamente<br>
                ✅ <strong>Windowing con Kafka states</strong> funcionando perfectamente<br>
                ⏳ <strong>Consumer Ruby</strong> cargando Rails para procesar eventos
            </p>
        </div>
    </div>
</body>
</html>
