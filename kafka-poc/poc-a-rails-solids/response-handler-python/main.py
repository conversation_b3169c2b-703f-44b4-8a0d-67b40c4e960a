import asyncio
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, Optional

import uvicorn
import httpx
from aiokafka import AIOKafkaConsumer
from fastapi import FastAPI
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Response Handler - POC A",
    description="Kafka Response Handler for WhatsApp responses",
    version="1.0.0"
)

class MIAResponse(BaseModel):
    conversation_id: str
    phone: str
    response: str
    processing_time_ms: int
    timestamp: str
    metadata: Optional[Dict] = None

class ResponseHandler:
    def __init__(self):
        self.kafka_brokers = os.getenv("KAFKA_BROKERS", "localhost:9092")
        self.whatsapp_url = os.getenv("WHATSAPP_SIMULATOR_URL", "http://localhost:3001")
        self.consumer_group = "response_handler"
        self.consumer = None
        self.running = False
        self.processed_count = 0
        
    async def start(self):
        """Start the response handler"""
        logger.info("🚀 Starting Response Handler...")
        
        # Initialize Kafka consumer for MIA responses
        self.consumer = AIOKafkaConsumer(
            'mia-responses',
            bootstrap_servers=self.kafka_brokers,
            group_id=self.consumer_group,
            auto_offset_reset='latest',
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            key_deserializer=lambda x: x.decode('utf-8') if x else None
        )
        
        await self.consumer.start()
        self.running = True
        logger.info("✅ Response Handler started successfully")
        
        # Start consuming responses
        try:
            await self.consume_responses()
        except Exception as e:
            logger.error(f"❌ Error in response consumption: {e}")
        finally:
            await self.stop()

    async def stop(self):
        """Stop the response handler gracefully"""
        logger.info("🛑 Stopping Response Handler...")
        self.running = False
        
        if self.consumer:
            await self.consumer.stop()
            
        logger.info(f"✅ Response Handler stopped. Processed {self.processed_count} responses.")

    async def consume_responses(self):
        """Main response consumption loop"""
        async for message in self.consumer:
            if not self.running:
                break
                
            try:
                await self.process_response(message.value, message.key)
                self.processed_count += 1
            except Exception as e:
                logger.error(f"❌ Error processing response: {e}")

    async def process_response(self, response_data: dict, phone: str):
        """Process a MIA response and send to WhatsApp"""
        try:
            # Parse response
            response = MIAResponse(**response_data)
            
            logger.info(f"📱 Sending response to {phone}: {response.response[:50]}...")
            
            # Send to WhatsApp simulator
            await self.send_to_whatsapp(response)
            
        except Exception as e:
            logger.error(f"❌ Failed to process response: {e}")

    async def send_to_whatsapp(self, response: MIAResponse):
        """Send response to WhatsApp simulator"""
        try:
            # Prepare WhatsApp message
            whatsapp_payload = {
                "phone": response.phone,
                "message": response.response,
                "conversation_id": response.conversation_id,
                "timestamp": response.timestamp,
                "source": "kafka_async"
            }
            
            # Send to WhatsApp simulator
            async with httpx.AsyncClient() as client:
                whatsapp_response = await client.post(
                    f"{self.whatsapp_url}/api/receive",
                    json=whatsapp_payload,
                    timeout=10.0
                )
                
                if whatsapp_response.status_code == 200:
                    logger.info(f"✅ Response sent to WhatsApp for {response.phone}")
                else:
                    logger.error(f"❌ WhatsApp API error: {whatsapp_response.status_code}")
                    
        except Exception as e:
            logger.error(f"❌ Failed to send to WhatsApp: {e}")

# Global response handler instance
response_handler = None

@app.on_event("startup")
async def startup_event():
    """Start the response handler on app startup"""
    global response_handler
    response_handler = ResponseHandler()
    
    # Start handler in background task
    asyncio.create_task(response_handler.start())

@app.on_event("shutdown")
async def shutdown_event():
    """Stop the response handler on app shutdown"""
    global response_handler
    if response_handler:
        await response_handler.stop()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "service": "Response Handler",
        "status": "running",
        "processed_responses": response_handler.processed_count if response_handler else 0
    }

@app.get("/health")
async def health():
    """Detailed health check"""
    return {
        "service": "Response Handler",
        "status": "healthy" if response_handler and response_handler.running else "unhealthy",
        "kafka_brokers": os.getenv("KAFKA_BROKERS", "localhost:9092"),
        "whatsapp_url": os.getenv("WHATSAPP_SIMULATOR_URL", "http://localhost:3001"),
        "processed_responses": response_handler.processed_count if response_handler else 0,
        "timestamp": datetime.utcnow().isoformat()
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False
    )
