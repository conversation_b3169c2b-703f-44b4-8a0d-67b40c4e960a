#!/bin/bash

# POC A Stop Script - Handles both HTTP and Kafka strategies
# Usage: ./stop-poc-a.sh [http|kafka|all]

STRATEGY=${1:-all}

echo "🛑 Stopping POC A"
echo "📋 Strategy: $STRATEGY"
echo ""

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found."
    exit 1
fi

# Validate strategy
if [[ "$STRATEGY" != "http" && "$STRATEGY" != "kafka" && "$STRATEGY" != "all" ]]; then
    echo "❌ Invalid strategy: $STRATEGY"
    echo "   Usage: $0 [http|kafka|all]"
    echo "   - http:  Stop only HTTP strategy services"
    echo "   - kafka: Stop only Kafka strategy services"
    echo "   - all:   Stop all services (default)"
    exit 1
fi

# Stop services based on strategy
if [ "$STRATEGY" = "kafka" ]; then
    echo "📦 Stopping Kafka strategy services..."
    docker-compose --profile kafka stop zookeeper kafka mia-consumer response-handler kafka-ui kafka-topic-creator
elif [ "$STRATEGY" = "http" ]; then
    echo "📦 Stopping HTTP strategy services..."
    docker-compose stop postgres rails-app redis sidekiq-worker mia-fastapi whatsapp-simulator adminer
else
    echo "📦 Stopping all POC A services..."
    docker-compose --profile kafka down
fi

echo ""
echo "✅ POC A ($STRATEGY) stopped successfully!"
echo ""
echo "📋 Useful Commands:"
echo "  Start HTTP:    ./start-poc-a.sh http"
echo "  Start Kafka:   ./start-poc-a.sh kafka"
echo "  Remove data:   docker-compose --profile kafka down -v"
echo ""
