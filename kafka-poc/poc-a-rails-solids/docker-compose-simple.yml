version: '3.7'

services:
  postgres:
    image: postgres:16
    hostname: postgres
    container_name: poc-a-postgres
    environment:
      POSTGRES_DB: mercately_poc
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  rails-app:
    build:
      context: ./rails-app
      dockerfile: Dockerfile
    hostname: rails-app
    container_name: poc-a-rails
    depends_on:
      - postgres
    environment:
      DATABASE_URL: ******************************************/mercately_poc
      RAILS_ENV: development
      RAILS_LOG_TO_STDOUT: "true"
      MIA_FASTAPI_URL: http://mia-fastapi:8000
      WHATSAPP_SIMULATOR_URL: http://whatsapp-simulator:3001
      MIA_STRATEGY: ${MIA_STRATEGY:-http}
      KAFKA_BROKERS: ${KAFKA_BROKERS:-kafka:29092}
    ports:
      - "3000:3000"
    volumes:
      - ./rails-app:/app
      - rails_gems:/usr/local/bundle
    restart: unless-stopped
    command: >
      bash -c "
        cd /app &&
        bundle install &&
        rm -f tmp/pids/server.pid &&
        ./bin/rails server -b 0.0.0.0 -p 3000
      "

  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: poc-a-redis
    ports:
      - "6380:6379"
    restart: unless-stopped

  sidekiq-worker:
    build:
      context: ./rails-app
      dockerfile: Dockerfile
    hostname: sidekiq-worker
    container_name: poc-a-sidekiq
    depends_on:
      - postgres
      - redis
      - rails-app
    environment:
      DATABASE_URL: ******************************************/mercately_poc
      REDIS_URL: redis://redis:6379/0
      RAILS_ENV: development
      MIA_FASTAPI_URL: http://mia-fastapi:8000
      WHATSAPP_SIMULATOR_URL: http://whatsapp-simulator:3001
      MIA_STRATEGY: ${MIA_STRATEGY:-http}
      KAFKA_BROKERS: ${KAFKA_BROKERS:-kafka:29092}
    volumes:
      - ./rails-app:/app
      - rails_gems:/usr/local/bundle
    command: >
      bash -c "
        cd /app &&
        bundle install &&
        echo 'Starting Sidekiq worker and silence detector...' &&
        bundle exec sidekiq &
        echo 'Sidekiq started in background' &&
        sleep 2 &&
        echo 'Starting silence detector...' &&
        ./bin/silence_detector
      "
    restart: unless-stopped

  mia-fastapi:
    build:
      context: ./mia-fastapi
      dockerfile: Dockerfile
    hostname: mia-fastapi
    container_name: poc-a-mia
    environment:
      LOG_LEVEL: INFO
    ports:
      - "8000:8000"
    volumes:
      - ./mia-fastapi:/app
    restart: unless-stopped

  whatsapp-simulator:
    build:
      context: ../shared/whatsapp-simulator
      dockerfile: Dockerfile
    hostname: whatsapp-simulator
    container_name: poc-a-simulator
    environment:
      RAILS_APP_URL: http://rails-app:3000
      LOG_LEVEL: INFO
    ports:
      - "3001:3001"
    volumes:
      - ../shared/whatsapp-simulator:/app
    restart: unless-stopped

  # Adminer for PostgreSQL
  adminer:
    image: adminer
    hostname: adminer
    container_name: poc-a-adminer
    depends_on:
      - postgres
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres

volumes:
  postgres_data:
  rails_gems:

networks:
  default:
    name: poc-a-network
