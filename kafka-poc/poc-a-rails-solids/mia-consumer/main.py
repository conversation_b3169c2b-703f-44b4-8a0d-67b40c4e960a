import asyncio
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Optional

import uvicorn
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from fastapi import FastAPI
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="MIA Kafka Consumer - POC A",
    description="MIA Processing Engine for Kafka Strategy",
    version="1.0.0"
)

# Pydantic models
class MessageData(BaseModel):
    content: str
    timestamp: str
    type: str = "text"

class ConversationRequest(BaseModel):
    conversation_id: str
    phone: str
    messages: List[MessageData]
    metadata: Optional[Dict] = None

class ConversationResponse(BaseModel):
    conversation_id: str
    phone: str
    response: str
    processing_time_ms: int
    timestamp: str
    metadata: Optional[Dict] = None

class MIAEngine:
    """Simulated MIA processing engine"""
    
    async def process_conversation(self, phone: str, conversation_id: str, messages: List[str], correlation_id: str = None) -> str:
        """Process conversation and return MIA response"""
        
        # Simulate processing time
        await asyncio.sleep(0.5)
        
        message_count = len(messages)
        combined_text = " + ".join(messages)
        
        # Generate contextual response
        if message_count == 1:
            response = f"Hola! Recibí tu mensaje: '{messages[0]}'. ¿En qué más puedo ayudarte?"
        else:
            response = f"Hola! Recibí {message_count} mensajes tuyos: {combined_text}. He procesado toda tu conversación y estoy aquí para ayudarte."
        
        logger.info(f"🤖 MIA processed {message_count} messages for {phone}: {response[:50]}...")
        
        return response

class MIAKafkaConsumer:
    def __init__(self):
        self.kafka_brokers = os.getenv("KAFKA_BROKERS", "localhost:9092")
        self.consumer_group = "mia_conversation_processor_v3"
        self.consumer = None
        self.producer = None
        self.running = False
        self.processing_count = 0
        
        # MIA processing engine
        self.mia_engine = MIAEngine()

    async def start(self):
        """Start the Kafka consumer and producer"""
        logger.info("🚀 Starting MIA Kafka Consumer...")
        
        # Initialize Kafka consumer
        self.consumer = AIOKafkaConsumer(
            'mia-requests',
            bootstrap_servers=self.kafka_brokers,
            group_id=f"mia_processor_{int(time.time())}",  # Unique group each time
            auto_offset_reset='earliest',
            enable_auto_commit=True,
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            key_deserializer=lambda x: x.decode('utf-8') if x else None
        )
        
        # Initialize Kafka producer
        self.producer = AIOKafkaProducer(
            bootstrap_servers=self.kafka_brokers,
            value_serializer=lambda x: json.dumps(x).encode('utf-8'),
            key_serializer=lambda x: x.encode('utf-8'),
            acks='all'
        )
        
        await self.consumer.start()
        await self.producer.start()
        
        self.running = True
        logger.info("✅ MIA Kafka Consumer started successfully")
        
        # Start consuming messages
        try:
            await self.consume_conversations()
        except Exception as e:
            logger.error(f"❌ Error in conversation consumption: {e}")
        finally:
            await self.stop()

    async def stop(self):
        """Stop the consumer gracefully"""
        logger.info("🛑 Stopping MIA Kafka Consumer...")
        self.running = False
        
        if self.consumer:
            await self.consumer.stop()
        if self.producer:
            await self.producer.stop()
            
        logger.info("✅ MIA Kafka Consumer stopped")

    async def consume_conversations(self):
        """Main conversation consumption loop"""
        async for message in self.consumer:
            if not self.running:
                break
                
            try:
                await self.process_conversation(message.value, message.key)
                self.processing_count += 1
            except Exception as e:
                logger.error(f"❌ Error processing conversation: {e}")

    async def process_conversation(self, conversation_data: dict, phone: str):
        """Process a conversation and send response"""
        try:
            start_time = time.time()
            
            # Parse conversation
            conversation = ConversationRequest(**conversation_data)
            
            logger.info(f"🤖 Processing conversation {conversation.conversation_id} "
                       f"for {phone} with {len(conversation.messages)} messages")
            
            # Extract message contents for MIA
            message_texts = [msg.content for msg in conversation.messages]
            
            # Process with MIA
            mia_response = await self.mia_engine.process_conversation(
                phone=phone,
                conversation_id=conversation.conversation_id,
                messages=message_texts,
                correlation_id=conversation.conversation_id
            )
            
            # Calculate processing time
            end_time = time.time()
            processing_time_ms = int((end_time - start_time) * 1000)
            
            # Create response
            response = ConversationResponse(
                conversation_id=conversation.conversation_id,
                phone=phone,
                response=mia_response,
                processing_time_ms=processing_time_ms,
                timestamp=datetime.utcnow().isoformat(),
                metadata={
                    "original_message_count": len(conversation.messages),
                    "processed_by": "MIA_KAFKA_CONSUMER",
                    "strategy": "kafka_async"
                }
            )
            
            # Send response back via Kafka
            await self.send_response(response)
            
        except Exception as e:
            logger.error(f"❌ Failed to process conversation: {e}")
            await self.send_error_response(
                conversation_data.get('conversation_id'), 
                phone, 
                str(e)
            )

    async def send_response(self, response: ConversationResponse):
        """Send response back via Kafka"""
        try:
            await self.producer.send(
                topic='mia-responses',
                key=response.phone,
                value=response.dict(),
                headers=[
                    ('conversation_id', response.conversation_id.encode()),
                    ('timestamp', str(int(datetime.utcnow().timestamp())).encode()),
                    ('source', 'mia_kafka_consumer'.encode())
                ]
            )
            
            logger.info(f"✅ Sent response {response.conversation_id} back via Kafka")
            
        except Exception as e:
            logger.error(f"❌ Failed to send response via Kafka: {e}")

    async def send_error_response(self, conversation_id: str, phone: str, error: str):
        """Send error response via Kafka"""
        try:
            error_response = {
                "conversation_id": conversation_id,
                "phone": phone,
                "response": f"Lo siento, hubo un error procesando tu mensaje: {error}",
                "processing_time_ms": 0,
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": {
                    "error": True,
                    "error_message": error,
                    "processed_by": "MIA_KAFKA_CONSUMER"
                }
            }
            
            await self.producer.send(
                topic='mia-responses',
                key=phone,
                value=error_response
            )
            
            logger.info(f"✅ Sent error response for {conversation_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to send error response: {e}")

# Global consumer instance
mia_consumer = None

@app.on_event("startup")
async def startup_event():
    """Start the Kafka consumer on app startup"""
    global mia_consumer
    mia_consumer = MIAKafkaConsumer()
    
    # Start consumer in background task
    asyncio.create_task(mia_consumer.start())

@app.on_event("shutdown")
async def shutdown_event():
    """Stop the Kafka consumer on app shutdown"""
    global mia_consumer
    if mia_consumer:
        await mia_consumer.stop()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "service": "MIA Kafka Consumer",
        "status": "running",
        "strategy": "kafka_async",
        "processed_conversations": mia_consumer.processing_count if mia_consumer else 0
    }

@app.get("/health")
async def health():
    """Detailed health check"""
    return {
        "service": "MIA Kafka Consumer",
        "status": "healthy" if mia_consumer and mia_consumer.running else "unhealthy",
        "kafka_brokers": os.getenv("KAFKA_BROKERS", "localhost:9092"),
        "processed_conversations": mia_consumer.processing_count if mia_consumer else 0,
        "timestamp": datetime.utcnow().isoformat()
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
