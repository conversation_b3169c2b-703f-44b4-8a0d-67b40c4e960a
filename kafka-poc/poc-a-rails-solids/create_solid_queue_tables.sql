-- Create SolidQueue tables
CREATE TABLE IF NOT EXISTS solid_queue_jobs (
    id BIGSERIAL PRIMARY KEY,
    queue_name VARCHAR NOT NULL,
    class_name VARCHAR NOT NULL,
    arguments TEXT,
    priority INTEGER DEFAULT 0 NOT NULL,
    active_job_id VARCHAR,
    scheduled_at TIMESTAMP,
    finished_at TIMESTAMP,
    concurrency_key TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_solid_queue_jobs_for_polling ON solid_queue_jobs (queue_name, finished_at);
CREATE INDEX IF NOT EXISTS index_solid_queue_jobs_on_class_name ON solid_queue_jobs (class_name);
CREATE INDEX IF NOT EXISTS index_solid_queue_jobs_on_active_job_id ON solid_queue_jobs (active_job_id);
CREATE INDEX IF NOT EXISTS index_solid_queue_jobs_on_scheduled_at ON solid_queue_jobs (scheduled_at);
CREATE INDEX IF NOT EXISTS index_solid_queue_jobs_for_alerting ON solid_queue_jobs (priority, created_at);
CREATE INDEX IF NOT EXISTS index_solid_queue_jobs_on_concurrency_key_priority_created_at ON solid_queue_jobs (concurrency_key, priority, created_at);

CREATE TABLE IF NOT EXISTS solid_queue_scheduled_executions (
    id BIGSERIAL PRIMARY KEY,
    job_id BIGINT NOT NULL REFERENCES solid_queue_jobs(id) ON DELETE CASCADE,
    queue_name VARCHAR NOT NULL,
    priority INTEGER DEFAULT 0 NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_solid_queue_scheduled_executions_for_polling ON solid_queue_scheduled_executions (scheduled_at, priority, job_id);

CREATE TABLE IF NOT EXISTS solid_queue_ready_executions (
    id BIGSERIAL PRIMARY KEY,
    job_id BIGINT NOT NULL REFERENCES solid_queue_jobs(id) ON DELETE CASCADE,
    queue_name VARCHAR NOT NULL,
    priority INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_solid_queue_ready_executions_for_polling ON solid_queue_ready_executions (priority, job_id);

CREATE TABLE IF NOT EXISTS solid_queue_claimed_executions (
    id BIGSERIAL PRIMARY KEY,
    job_id BIGINT NOT NULL REFERENCES solid_queue_jobs(id) ON DELETE CASCADE,
    process_id BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_solid_queue_claimed_executions_on_process_id_and_job_id ON solid_queue_claimed_executions (process_id, job_id);

CREATE TABLE IF NOT EXISTS solid_queue_blocked_executions (
    id BIGSERIAL PRIMARY KEY,
    job_id BIGINT NOT NULL REFERENCES solid_queue_jobs(id) ON DELETE CASCADE,
    queue_name VARCHAR NOT NULL,
    priority INTEGER DEFAULT 0 NOT NULL,
    concurrency_key VARCHAR NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_solid_queue_blocked_executions_for_maintenance ON solid_queue_blocked_executions (expires_at);
CREATE INDEX IF NOT EXISTS index_solid_queue_blocked_executions_for_polling ON solid_queue_blocked_executions (concurrency_key, priority, job_id);

CREATE TABLE IF NOT EXISTS solid_queue_failed_executions (
    id BIGSERIAL PRIMARY KEY,
    job_id BIGINT NOT NULL REFERENCES solid_queue_jobs(id) ON DELETE CASCADE,
    error TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS solid_queue_pauses (
    id BIGSERIAL PRIMARY KEY,
    queue_name VARCHAR NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS index_solid_queue_pauses_on_queue_name ON solid_queue_pauses (queue_name);

CREATE TABLE IF NOT EXISTS solid_queue_processes (
    id BIGSERIAL PRIMARY KEY,
    kind VARCHAR NOT NULL,
    last_heartbeat_at TIMESTAMP NOT NULL,
    supervisor_id BIGINT,
    pid INTEGER NOT NULL,
    hostname VARCHAR,
    metadata TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_solid_queue_processes_on_last_heartbeat ON solid_queue_processes (last_heartbeat_at);
CREATE INDEX IF NOT EXISTS index_solid_queue_processes_on_kind ON solid_queue_processes (kind, last_heartbeat_at);
CREATE INDEX IF NOT EXISTS index_solid_queue_processes_on_supervisor ON solid_queue_processes (supervisor_id);

CREATE TABLE IF NOT EXISTS solid_queue_semaphores (
    id BIGSERIAL PRIMARY KEY,
    key VARCHAR NOT NULL,
    value INTEGER DEFAULT 1 NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS index_solid_queue_semaphores_on_key_and_value ON solid_queue_semaphores (key, value);
CREATE INDEX IF NOT EXISTS index_solid_queue_semaphores_on_expires_at ON solid_queue_semaphores (expires_at);
