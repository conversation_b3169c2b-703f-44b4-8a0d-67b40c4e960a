<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windowing en Kafka - Funcionamiento Detallado</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 40px;
            font-style: italic;
        }
        h2 {
            color: #34495e;
            border-left: 5px solid #3498db;
            padding-left: 20px;
            margin-top: 50px;
            font-size: 1.8em;
        }
        .step-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
        }
        .step-title {
            color: #495057;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .json-example {
            background: #1e3a8a;
            color: #bfdbfe;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -37px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #667eea;
        }
        .timeline-time {
            color: #667eea;
            font-weight: bold;
            font-size: 0.9em;
        }
        .highlight {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ff6b6b;
        }
        .mermaid {
            text-align: center;
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .topic-flow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .topic-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .topic-name {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .topic-desc {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Windowing en Kafka</h1>
        <p class="subtitle">Funcionamiento Detallado del State Store y Event Aggregation</p>

        <h2>🎯 Concepto Clave: Session Windowing</h2>
        <div class="highlight">
            <h3>¿Qué es Session Windowing?</h3>
            <p><strong>Session Windowing</strong> es una técnica que agrupa mensajes de un usuario en "ventanas" basadas en períodos de actividad. Cuando hay <strong>10 segundos de silencio</strong>, se cierra la ventana y se procesa como una conversación completa.</p>
            
            <p><strong>Ventaja:</strong> En lugar de procesar cada mensaje individualmente, MIA recibe conversaciones completas con contexto.</p>
        </div>

        <h2>📊 Flujo Detallado Paso a Paso</h2>
        
        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-time">T+0s: Mensaje 1 llega</div>
                <h4>1. Creación de Sesión</h4>
                <div class="code-block">
# SessionWindowManager.process_message("+549112345678", message_data)

def create_session(user_id, message, current_time)
  new_session = {
    'user_id' => user_id,
    'messages' => [message],                    # Array con 1 mensaje
    'window_start' => current_time.iso8601,     # "2025-08-11T02:30:00Z"
    'last_activity' => current_time.iso8601,    # "2025-08-11T02:30:00Z"
    'expires_at' => (current_time + 10.seconds).iso8601  # "2025-08-11T02:30:10Z"
  }
  
  @state_store.put(user_id, new_session)  # Guarda en Kafka state store
end
                </div>
                
                <h4>Estado en Kafka Topic: whatsapp-sessions-state</h4>
                <div class="json-example">
Key: "+549112345678"
Value: {
  "user_id": "+549112345678",
  "messages": [
    {
      "content": "Hola, necesito ayuda",
      "timestamp": "2025-08-11T02:30:00Z",
      "type": "text"
    }
  ],
  "window_start": "2025-08-11T02:30:00Z",
  "last_activity": "2025-08-11T02:30:00Z",
  "expires_at": "2025-08-11T02:30:10Z"
}
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-time">T+5s: Mensaje 2 llega</div>
                <h4>2. Extensión de Sesión</h4>
                <div class="code-block">
# Nuevo mensaje llega ANTES de que expire (T+5s < T+10s)

def extend_session(user_id, session, message, current_time)
  session['messages'] << message                # Agrega mensaje al array
  session['last_activity'] = current_time.iso8601
  session['expires_at'] = (current_time + 10.seconds).iso8601  # RESET timer!
  
  @state_store.put(user_id, session)  # Actualiza en Kafka
end
                </div>
                
                <h4>Estado Actualizado en Kafka:</h4>
                <div class="json-example">
Key: "+549112345678"
Value: {
  "user_id": "+549112345678",
  "messages": [
    {
      "content": "Hola, necesito ayuda",
      "timestamp": "2025-08-11T02:30:00Z",
      "type": "text"
    },
    {
      "content": "Es urgente por favor",
      "timestamp": "2025-08-11T02:30:05Z",
      "type": "text"
    }
  ],
  "window_start": "2025-08-11T02:30:00Z",
  "last_activity": "2025-08-11T02:30:05Z",
  "expires_at": "2025-08-11T02:30:15Z"  ← Timer reseteado!
}
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-time">T+15s: Timer expira</div>
                <h4>3. Cierre de Ventana y Agregación</h4>
                <div class="code-block">
# 10 segundos de silencio → cierra ventana

def close_expired_session(user_id, session)
  # Crea evento agregado
  aggregated_event = {
    user_id: user_id,
    messages: session['messages'],              # Todos los mensajes
    message_count: session['messages'].size,   # 2
    window_start: session['window_start'],
    window_end: session['last_activity'],
    closed_at: Time.now.iso8601,
    windowing_method: 'kafka_state_store'
  }

  # Publica a topic de eventos agregados
  @producer.produce(
    aggregated_event.to_json,
    topic: 'whatsapp_aggregated_events',
    key: user_id
  )
  
  # Elimina sesión del state store
  @state_store.delete(user_id)
end
                </div>
                
                <h4>Evento Publicado en whatsapp_aggregated_events:</h4>
                <div class="json-example">
Key: "+549112345678"
Value: {
  "user_id": "+549112345678",
  "messages": [
    {
      "content": "Hola, necesito ayuda",
      "timestamp": "2025-08-11T02:30:00Z",
      "type": "text"
    },
    {
      "content": "Es urgente por favor",
      "timestamp": "2025-08-11T02:30:05Z",
      "type": "text"
    }
  ],
  "message_count": 2,
  "window_start": "2025-08-11T02:30:00Z",
  "window_end": "2025-08-11T02:30:05Z",
  "closed_at": "2025-08-11T02:30:15Z",
  "windowing_method": "kafka_state_store"
}
                </div>
            </div>
        </div>

        <h2>🗄️ Kafka Topics y su Función</h2>
        <div class="topic-flow">
            <div class="topic-card">
                <div class="topic-name">whatsapp-sessions-state</div>
                <div class="topic-desc">
                    <strong>Compacted Topic</strong><br>
                    Almacena estado de sesiones activas<br>
                    Key: user_id<br>
                    Value: session_data
                </div>
            </div>
            <div class="topic-card">
                <div class="topic-name">whatsapp_aggregated_events</div>
                <div class="topic-desc">
                    <strong>Event Topic</strong><br>
                    Eventos de ventanas cerradas<br>
                    Key: user_id<br>
                    Value: aggregated_event
                </div>
            </div>
        </div>

        <h2>⚙️ Componentes Técnicos Clave</h2>

        <div class="step-container">
            <div class="step-title">1. KafkaStateStore - Persistencia de Estado</div>
            <div class="code-block">
class KafkaStateStore
  def put(key, value)
    # Cache en memoria para lecturas rápidas
    @memory_cache[key] = value

    # Persiste en Kafka para durabilidad
    @producer.produce(
      topic: @topic_name,           # "whatsapp-sessions-state"
      key: key,                     # "+549112345678"
      payload: value.to_json        # session_data
    )
  end

  def delete(key)
    @memory_cache.delete(key)

    # Tombstone record (payload: nil) para eliminación
    @producer.produce(
      topic: @topic_name,
      key: key,
      payload: nil                  # ← Elimina el registro
    )
  end
end
            </div>
        </div>

        <div class="step-container">
            <div class="step-title">2. SessionWindowManager - Lógica de Windowing</div>
            <div class="code-block">
class SessionWindowManager
  WINDOW_DURATION = 10.seconds

  def process_message(user_id, message_data)
    existing_session = @state_store.get(user_id)

    if existing_session && session_active?(existing_session)
      extend_session(user_id, existing_session, message_data)  # Reset timer
    else
      create_session(user_id, message_data)                    # Nueva ventana
    end
  end

  # Thread que verifica expiraciones cada segundo
  def start_expiration_checker
    Thread.new do
      loop do
        @state_store.scan_all_sessions do |user_id, session|
          check_and_close_if_expired(user_id)
        end
        sleep(1)
      end
    end
  end
end
            </div>
        </div>

        <h2>🔄 Diagrama de Flujo de Datos</h2>
        <div class="step-container">
            <div class="mermaid">
sequenceDiagram
    participant U as Usuario
    participant R as Rails
    participant SWM as SessionWindowManager
    participant KSS as KafkaStateStore
    participant KT1 as whatsapp-sessions-state
    participant KT2 as whatsapp_aggregated_events
    participant C as Consumer

    U->>R: Mensaje 1: "Hola"
    R->>SWM: process_message(user_id, msg1)
    SWM->>KSS: put(user_id, new_session)
    KSS->>KT1: Produce session_data

    Note over SWM: Timer: 10 segundos

    U->>R: Mensaje 2: "Ayuda" (T+5s)
    R->>SWM: process_message(user_id, msg2)
    SWM->>KSS: put(user_id, updated_session)
    KSS->>KT1: Update session_data

    Note over SWM: Timer: Reset a 10 segundos

    Note over SWM: 10 segundos de silencio...

    SWM->>SWM: check_expiration()
    SWM->>KSS: get(user_id)
    KSS-->>SWM: session_data
    SWM->>KT2: Produce aggregated_event
    SWM->>KSS: delete(user_id)
    KSS->>KT1: Tombstone record

    C->>KT2: Consume aggregated_event
    C->>C: Process conversation
            </div>
        </div>

        <h2>📈 Ventajas del Windowing con Kafka</h2>
        <div class="highlight">
            <h3>🎯 Beneficios Clave:</h3>
            <ul>
                <li><strong>Persistencia:</strong> Estado sobrevive a reinicios del sistema</li>
                <li><strong>Escalabilidad:</strong> Kafka maneja millones de sesiones concurrentes</li>
                <li><strong>Auditabilidad:</strong> Todos los eventos están registrados</li>
                <li><strong>Exactamente una vez:</strong> Kafka garantiza no duplicación</li>
                <li><strong>Orden garantizado:</strong> Por partition key (user_id)</li>
                <li><strong>Compactación:</strong> Solo mantiene el estado más reciente</li>
            </ul>
        </div>

        <h2>🔍 Estado Actual del Sistema</h2>
        <div class="step-container">
            <div class="code-block">
# Verificar eventos generados
$ kafka-run-class kafka.tools.GetOffsetShell --topic whatsapp_aggregated_events
whatsapp_aggregated_events:0:10

# ✅ 10 eventos agregados generados exitosamente
# ✅ Windowing con Kafka states funcionando
# ⏳ Consumer Ruby cargando Rails para procesar eventos
            </div>
        </div>

        <script>
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                sequence: {
                    useMaxWidth: true,
                    wrap: true,
                    messageFontSize: 14,
                    actorFontSize: 14
                }
            });
        </script>
    </div>
</body>
</html>
