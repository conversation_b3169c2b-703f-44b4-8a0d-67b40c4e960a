#!/bin/bash

# POC A Dynamic Strategy Demonstration
# This script demonstrates the unified architecture with dynamic strategy switching

echo "🎯 POC A Dynamic Strategy Demonstration"
echo "======================================="
echo ""
echo "✨ NEW: Unified architecture with DYNAMIC strategy switching!"
echo ""
echo "📡 HTTP Strategy:"
echo "   - Direct synchronous communication with MIA"
echo "   - Flow: Messages → Windowing → HTTP Request → MIA → Response"
echo "   - Pros: Simple, fast, direct"
echo ""
echo "🚀 Kafka Strategy:"
echo "   - Asynchronous communication via Kafka"
echo "   - Flow: Messages → Windowing → Kafka → MIA Consumer → Kafka → Response Handler"
echo "   - Pros: Scalable, non-blocking, resilient"
echo ""
echo "🔄 Dynamic Switching:"
echo "   - Both strategies run simultaneously"
echo "   - Switch between them via web interface"
echo "   - Compare performance in real-time"
echo ""

# Function to show current strategy
show_current_strategy() {
    if docker-compose ps | grep -q "poc-a-kafka"; then
        if [ "$(docker-compose ps poc-a-kafka | grep -c Up)" -gt 0 ]; then
            echo "   Current: 🚀 Kafka Strategy (running)"
        else
            echo "   Current: 🚀 Kafka Strategy (stopped)"
        fi
    else
        if docker-compose ps | grep -q "poc-a-rails"; then
            if [ "$(docker-compose ps poc-a-rails | grep -c Up)" -gt 0 ]; then
                echo "   Current: 📡 HTTP Strategy (running)"
            else
                echo "   Current: 📡 HTTP Strategy (stopped)"
            fi
        else
            echo "   Current: ⭕ No strategy running"
        fi
    fi
}

show_current_strategy

echo ""
echo "📋 Available Commands:"
echo ""
echo "1. Start with HTTP Strategy:"
echo "   ./start-poc-a.sh http"
echo ""
echo "2. Start with Kafka Strategy:"
echo "   ./start-poc-a.sh kafka"
echo ""
echo "3. Stop current strategy:"
echo "   ./stop-poc-a.sh"
echo ""
echo "4. Switch from HTTP to Kafka:"
echo "   ./stop-poc-a.sh http && ./start-poc-a.sh kafka"
echo ""
echo "5. Switch from Kafka to HTTP:"
echo "   ./stop-poc-a.sh kafka && ./start-poc-a.sh http"
echo ""
echo "🧪 Testing Both Strategies:"
echo ""
echo "1. Start with HTTP strategy:"
echo "   ./start-poc-a.sh http"
echo "   - Open http://localhost:3001 (WhatsApp Simulator)"
echo "   - Send messages and observe direct HTTP processing"
echo "   - Check logs: docker-compose logs -f rails-app"
echo ""
echo "2. Switch to Kafka strategy:"
echo "   ./stop-poc-a.sh http && ./start-poc-a.sh kafka"
echo "   - Send same messages and observe Kafka processing"
echo "   - Monitor Kafka UI: http://localhost:8081"
echo "   - Check logs: docker-compose --profile kafka logs -f"
echo ""
echo "📊 Monitoring URLs:"
echo "   - WhatsApp Simulator: http://localhost:3001"
echo "   - Rails Dashboard:    http://localhost:3000"
echo "   - MIA FastAPI:        http://localhost:8000/docs"
echo "   - Kafka UI:           http://localhost:8081 (Kafka strategy only)"
echo "   - PostgreSQL Admin:   http://localhost:8080"
echo ""
echo "🔍 Performance Comparison:"
echo "   Both strategies use the same windowing logic and database."
echo "   Compare response times and throughput in the Rails dashboard."
echo ""

# Interactive mode
read -p "🤔 Would you like to start a strategy now? [http/kafka/n]: " choice

case $choice in
    http)
        echo "🚀 Starting HTTP strategy..."
        ./start-poc-a.sh http
        ;;
    kafka)
        echo "🚀 Starting Kafka strategy..."
        ./start-poc-a.sh kafka
        ;;
    *)
        echo "👋 No problem! Use the commands above when you're ready."
        ;;
esac

echo ""
echo "✅ Demo script completed!"
echo ""
