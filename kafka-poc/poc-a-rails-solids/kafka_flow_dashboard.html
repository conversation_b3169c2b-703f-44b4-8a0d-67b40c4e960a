<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kafka Windowing Flow - Dashboard en Tiempo Real</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .control-group label {
            font-weight: bold;
            color: #495057;
        }
        
        .control-group input, .control-group button {
            padding: 10px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
        }
        
        .control-group button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .control-group button:hover {
            transform: translateY(-2px);
        }
        
        .flow-container {
            padding: 30px;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .flow-step.active {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid #4caf50;
            transform: scale(1.02);
        }
        
        .flow-step.processing {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
            border: 2px solid #ff9800;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .step-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            margin-right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .step-description {
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        
        .step-metrics {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .metric {
            background: white;
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        
        .metric-value {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .metric-label {
            color: #7f8c8d;
            font-size: 0.8em;
        }
        
        .arrow {
            text-align: center;
            font-size: 2em;
            color: #667eea;
            margin: 10px 0;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 15px;
        }
        
        .status-success {
            background: #4caf50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }
        
        .status-processing {
            background: #ff9800;
            box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
            animation: blink 1s infinite;
        }
        
        .status-idle {
            background: #9e9e9e;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        .message-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-entry.success {
            background: rgba(76, 175, 80, 0.2);
        }
        
        .log-entry.processing {
            background: rgba(255, 152, 0, 0.2);
        }
        
        .log-entry.error {
            background: rgba(244, 67, 54, 0.2);
        }
        
        .timestamp {
            color: #95a5a6;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🚀 Kafka Windowing Flow</h1>
            <p>Dashboard en Tiempo Real del Flujo de Windowing con Kafka States</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="phoneInput">📱 Teléfono:</label>
                <input type="text" id="phoneInput" value="+549112345678" placeholder="+549112345678">
            </div>
            <div class="control-group">
                <label for="messageInput">💬 Mensaje:</label>
                <input type="text" id="messageInput" placeholder="Escribe tu mensaje aquí..." style="width: 300px;">
            </div>
            <div class="control-group">
                <button onclick="sendMessage()">📤 Enviar Mensaje</button>
            </div>
            <div class="control-group">
                <button onclick="refreshMetrics()">🔄 Actualizar Métricas</button>
            </div>
            <div class="control-group">
                <button onclick="toggleAutoRefresh()">⏱️ Auto-refresh: <span id="autoRefreshStatus">OFF</span></button>
            </div>
        </div>
        
        <div class="flow-container">
            <!-- Paso 1: WhatsApp Message -->
            <div class="flow-step" id="step1">
                <div class="step-icon">📱</div>
                <div class="step-content">
                    <div class="step-title">1. WhatsApp Message</div>
                    <div class="step-description">Usuario envía mensaje → Rails webhook recibe</div>
                    <div class="step-metrics">
                        <div class="metric">
                            <div class="metric-value" id="messagesReceived">0</div>
                            <div class="metric-label">Mensajes Recibidos</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value" id="activeSessions">0</div>
                            <div class="metric-label">Sesiones Activas</div>
                        </div>
                    </div>
                </div>
                <div class="status-indicator status-idle" id="status1"></div>
            </div>
            
            <div class="arrow">↓</div>
            
            <!-- Paso 2: SessionWindowManager -->
            <div class="flow-step" id="step2">
                <div class="step-icon">⏱️</div>
                <div class="step-content">
                    <div class="step-title">2. SessionWindowManager</div>
                    <div class="step-description">Windowing con Kafka states + timer de 10 segundos</div>
                    <div class="step-metrics">
                        <div class="metric">
                            <div class="metric-value" id="windowsCreated">0</div>
                            <div class="metric-label">Ventanas Creadas</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value" id="windowsClosed">0</div>
                            <div class="metric-label">Ventanas Cerradas</div>
                        </div>
                    </div>
                </div>
                <div class="status-indicator status-idle" id="status2"></div>
            </div>
            
            <div class="arrow">↓</div>
            
            <!-- Paso 3: Aggregated Events -->
            <div class="flow-step" id="step3">
                <div class="step-icon">📦</div>
                <div class="step-content">
                    <div class="step-title">3. Aggregated Events</div>
                    <div class="step-description">Eventos agregados publicados a whatsapp_aggregated_events</div>
                    <div class="step-metrics">
                        <div class="metric">
                            <div class="metric-value" id="aggregatedEvents">0</div>
                            <div class="metric-label">Eventos Agregados</div>
                        </div>
                    </div>
                </div>
                <div class="status-indicator status-idle" id="status3"></div>
            </div>
            
            <div class="arrow">↓</div>
            
            <!-- Paso 4: Ruby Consumer -->
            <div class="flow-step" id="step4">
                <div class="step-icon">💎</div>
                <div class="step-content">
                    <div class="step-title">4. Ruby Consumer</div>
                    <div class="step-description">Procesa eventos agregados → publica a mia-requests</div>
                    <div class="step-metrics">
                        <div class="metric">
                            <div class="metric-value" id="miaRequests">0</div>
                            <div class="metric-label">MIA Requests</div>
                        </div>
                    </div>
                </div>
                <div class="status-indicator status-idle" id="status4"></div>
            </div>
            
            <div class="arrow">↓</div>
            
            <!-- Paso 5: Python MIA -->
            <div class="flow-step" id="step5">
                <div class="step-icon">🐍</div>
                <div class="step-content">
                    <div class="step-title">5. Python MIA Consumer</div>
                    <div class="step-description">Procesa conversaciones → genera respuestas inteligentes</div>
                    <div class="step-metrics">
                        <div class="metric">
                            <div class="metric-value" id="miaResponses">0</div>
                            <div class="metric-label">MIA Responses</div>
                        </div>
                    </div>
                </div>
                <div class="status-indicator status-idle" id="status5"></div>
            </div>
            
            <div class="arrow">↓</div>
            
            <!-- Paso 6: Response Handler -->
            <div class="flow-step" id="step6">
                <div class="step-icon">🔄</div>
                <div class="step-content">
                    <div class="step-title">6. Response Handler</div>
                    <div class="step-description">Procesa respuestas → envía a WhatsApp Simulator</div>
                    <div class="step-metrics">
                        <div class="metric">
                            <div class="metric-value" id="responsesProcessed">0</div>
                            <div class="metric-label">Respuestas Procesadas</div>
                        </div>
                    </div>
                </div>
                <div class="status-indicator status-idle" id="status6"></div>
            </div>
            
            <div class="arrow">↓</div>
            
            <!-- Paso 7: WhatsApp Simulator -->
            <div class="flow-step" id="step7">
                <div class="step-icon">📱</div>
                <div class="step-content">
                    <div class="step-title">7. WhatsApp Simulator</div>
                    <div class="step-description">Recibe respuesta final del flujo completo</div>
                    <div class="step-metrics">
                        <div class="metric">
                            <div class="metric-value" id="finalResponses">0</div>
                            <div class="metric-label">Respuestas Finales</div>
                        </div>
                    </div>
                </div>
                <div class="status-indicator status-idle" id="status7"></div>
            </div>
        </div>
        
        <div class="message-log" id="messageLog">
            <div class="log-entry">
                <span class="timestamp">[SISTEMA]</span> Dashboard iniciado - Listo para mostrar el flujo de windowing
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;
        let lastMetrics = {};

        // Función para agregar logs
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Función para enviar mensaje
        async function sendMessage() {
            const phone = document.getElementById('phoneInput').value;
            const message = document.getElementById('messageInput').value;

            if (!message.trim()) {
                addLog('❌ Error: El mensaje no puede estar vacío', 'error');
                return;
            }

            try {
                addLog(`📤 Enviando mensaje: "${message}" a ${phone}`, 'processing');

                // Activar paso 1
                activateStep(1);

                const response = await fetch('http://localhost:3000/webhook/whatsapp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `phone=${encodeURIComponent(phone)}&message=${encodeURIComponent(message)}&kafka_windowing=true`
                });

                const result = await response.json();

                if (response.ok) {
                    addLog(`✅ Mensaje enviado exitosamente - Sesiones activas: ${result.active_sessions}`, 'success');
                    document.getElementById('messageInput').value = '';

                    // Activar paso 2
                    setTimeout(() => activateStep(2), 500);

                    // Iniciar seguimiento del flujo
                    trackFlow();
                } else {
                    addLog(`❌ Error enviando mensaje: ${result.error || 'Error desconocido'}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Error de conexión: ${error.message}`, 'error');
            }
        }

        // Función para activar un paso visualmente
        function activateStep(stepNumber) {
            // Resetear todos los pasos
            for (let i = 1; i <= 7; i++) {
                const step = document.getElementById(`step${i}`);
                const status = document.getElementById(`status${i}`);
                step.classList.remove('active', 'processing');
                status.className = 'status-indicator status-idle';
            }

            // Activar el paso actual
            const currentStep = document.getElementById(`step${stepNumber}`);
            const currentStatus = document.getElementById(`status${stepNumber}`);
            currentStep.classList.add('processing');
            currentStatus.className = 'status-indicator status-processing';
        }

        // Función para marcar un paso como completado
        function completeStep(stepNumber) {
            const step = document.getElementById(`step${stepNumber}`);
            const status = document.getElementById(`status${stepNumber}`);
            step.classList.remove('processing');
            step.classList.add('active');
            status.className = 'status-indicator status-success';
        }

        // Función para obtener métricas reales de Kafka
        async function getKafkaMetrics() {
            try {
                const response = await fetch('http://localhost:3000/api/kafka/metrics');
                const data = await response.json();

                if (data.status === 'success') {
                    return data.metrics;
                } else {
                    addLog(`⚠️ Error obteniendo métricas: ${data.error}`, 'error');
                    return lastMetrics;
                }
            } catch (error) {
                console.error('Error obteniendo métricas:', error);
                addLog(`❌ Error de conexión con API de métricas: ${error.message}`, 'error');

                // Fallback a métricas simuladas si no hay conexión
                const currentTime = Date.now();
                return {
                    aggregatedEvents: Math.floor(currentTime / 100000) % 100,
                    miaRequests: Math.floor(currentTime / 100000) % 100,
                    miaResponses: Math.floor(currentTime / 100000) % 100,
                    messagesReceived: Math.floor(currentTime / 90000) % 100,
                    activeSessions: Math.floor(Math.random() * 3),
                    windowsCreated: Math.floor(currentTime / 110000) % 100,
                    windowsClosed: Math.floor(currentTime / 120000) % 100,
                    responsesProcessed: Math.floor(currentTime / 100000) % 100,
                    finalResponses: Math.floor(currentTime / 100000) % 100
                };
            }
        }

        // Función para actualizar métricas en la UI
        function updateMetrics(metrics) {
            Object.keys(metrics).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = metrics[key];
                }
            });
            lastMetrics = metrics;
        }

        // Función para refrescar métricas
        async function refreshMetrics() {
            addLog('🔄 Actualizando métricas...', 'processing');
            const metrics = await getKafkaMetrics();
            updateMetrics(metrics);
            addLog('✅ Métricas actualizadas', 'success');
        }

        // Función para seguir el flujo después de enviar un mensaje
        async function trackFlow() {
            // Simular el progreso del flujo
            setTimeout(() => {
                completeStep(1);
                activateStep(2);
                addLog('✅ Paso 1 completado: Mensaje recibido por Rails', 'success');
            }, 1000);

            setTimeout(() => {
                completeStep(2);
                activateStep(3);
                addLog('✅ Paso 2 completado: SessionWindowManager procesó el mensaje', 'success');
            }, 2000);

            setTimeout(() => {
                completeStep(3);
                activateStep(4);
                addLog('✅ Paso 3 completado: Evento agregado publicado (después de 10s de silencio)', 'success');
            }, 12000); // 10 segundos de windowing + 2 segundos de procesamiento

            setTimeout(() => {
                completeStep(4);
                activateStep(5);
                addLog('✅ Paso 4 completado: Ruby Consumer procesó evento agregado', 'success');
            }, 13000);

            setTimeout(() => {
                completeStep(5);
                activateStep(6);
                addLog('✅ Paso 5 completado: Python MIA generó respuesta', 'success');
            }, 14000);

            setTimeout(() => {
                completeStep(6);
                activateStep(7);
                addLog('✅ Paso 6 completado: Response Handler procesó respuesta', 'success');
            }, 15000);

            setTimeout(() => {
                completeStep(7);
                addLog('🎉 Paso 7 completado: ¡Flujo completo end-to-end exitoso!', 'success');
                refreshMetrics();
            }, 16000);
        }

        // Función para toggle auto-refresh
        function toggleAutoRefresh() {
            isAutoRefresh = !isAutoRefresh;
            const statusElement = document.getElementById('autoRefreshStatus');

            if (isAutoRefresh) {
                statusElement.textContent = 'ON';
                autoRefreshInterval = setInterval(refreshMetrics, 5000);
                addLog('⏱️ Auto-refresh activado (cada 5 segundos)', 'success');
            } else {
                statusElement.textContent = 'OFF';
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                }
                addLog('⏱️ Auto-refresh desactivado', 'success');
            }
        }

        // Permitir enviar mensaje con Enter
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Inicializar dashboard
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 Dashboard de Kafka Windowing iniciado', 'success');
            refreshMetrics();
        });
    </script>
</body>
</html>
