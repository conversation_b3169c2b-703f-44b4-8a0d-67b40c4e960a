# AggregatedEventsConsumer - Processes windowed message events for MIA
# Phase 2: Replace ProcessMessageWindowJob with Karafka consumer
require 'kafka'

class AggregatedEventsConsumer < Karafka::BaseConsumer
  def consume
    messages.each do |message|
      process_aggregated_event(message)
    end
  end

  private

  def process_aggregated_event(message)
    begin
      # Parse JSON manually since we removed deserializer
      event_data = JSON.parse(message.raw_payload)
      user_id = message.key
      
      Rails.logger.info "🔄 KARAFKA WINDOWING: Processing aggregated event for #{user_id} (#{event_data['message_count']} messages)"
      
      # Create conversation record (same as ProcessMessageWindowJob)
      conversation = create_conversation_from_event(event_data)

      Rails.logger.info "🎯 KARAFKA WINDOWING: Publishing conversation #{conversation.id} to mia-requests (unified flow)"
      Rails.logger.info "🔄 WINDOWING: Kafka State Store (#{event_data['windowing_method']})"

      # Publish directly to mia-requests (same topic as POC A)
      publish_to_mia_requests(conversation)

      Rails.logger.info "✅ KARAFKA WINDOWING: Event processed and published to mia-requests successfully"
      
    rescue => e
      Rails.logger.error "❌ KARAFKA WINDOWING: Error processing aggregated event: #{e.message}"
      Rails.logger.error "❌ Event: #{message.raw_payload[0..100]}..."
    end
  end

  def create_conversation_from_event(event_data)
    # Create conversation record similar to Conversation.create_from_window!
    correlation_id = SecureRandom.uuid
    
    conversation_params = {
      correlation_id: correlation_id,
      phone: event_data['user_id'],
      messages: event_data['messages'],
      message_count: event_data['message_count'],
      window_start: event_data['window_start'],
      window_end: event_data['window_end'],
      status: 'pending',
      metadata: {
        windowing_method: event_data['windowing_method'],
        closed_at: event_data['closed_at'],
        source: 'karafka_windowing'
      }
    }
    
    Conversation.create!(conversation_params)
  end

  def publish_to_mia_requests(conversation)
    # Use ruby-kafka for producing (more reliable)
    kafka = Kafka.new(
      seed_brokers: [ENV.fetch('KAFKA_BROKERS', 'kafka:29092')],
      client_id: 'karafka-aggregated-events-producer'
    )

    # Format message same as POC A
    message_data = {
      conversation_id: conversation.correlation_id,
      phone: conversation.phone,
      messages: conversation.messages.map do |msg|
        {
          content: msg['content'],
          timestamp: msg['timestamp'],
          type: msg['type'] || 'text'
        }
      end,
      metadata: {
        strategy: 'karafka_windowing',
        sent_to_kafka_at: Time.current.iso8601,
        rails_conversation_id: conversation.id,
        windowing_method: 'kafka_state_store'
      }
    }

    # Publish to mia-requests (same topic as POC A) using ruby-kafka
    producer = kafka.producer
    producer.produce(
      message_data.to_json,
      topic: 'mia-requests',
      key: conversation.phone
    )
    producer.deliver_messages

    Rails.logger.info "📤 Published to mia-requests: #{conversation.correlation_id}"

  ensure
    producer&.shutdown
    kafka&.close
  end
end
