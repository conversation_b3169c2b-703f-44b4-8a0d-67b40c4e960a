# MiaResponseConsumer - Karafka consumer to replace KafkaResponseConsumerJob
# Phase 1: Introduction of Karafka for response handling
class MiaResponseConsumer < Karafka::BaseConsumer
  def consume
    messages.each do |message|
      process_mia_response(message)
    end
  end

  private

  def process_mia_response(message)
    begin
      # Parse response data (same logic as KafkaResponseConsumerJob)
      # Parse JSON manually since we removed deserializer
      response_data = JSON.parse(message.raw_payload)
      phone = message.key
      
      Rails.logger.info "📱 KARAFKA: Processing MIA response for #{phone}: #{response_data['response'][0..50]}..."
      Rails.logger.info "🔄 EVOLUTION: Using Karafka instead of Sidekiq job"
      
      # Send to WhatsApp simulator (reuse existing logic)
      send_to_whatsapp(response_data)
      
    rescue => e
      Rails.logger.error "❌ KARAFKA: Error processing MIA response: #{e.message}"
      Rails.logger.error "❌ Message: #{message.raw_payload[0..100]}..."
    end
  end

  def send_to_whatsapp(response_data)
    begin
      whatsapp_url = ENV['WHATSAPP_SIMULATOR_URL'] || 'http://whatsapp-simulator:3001'
      
      # Prepare WhatsApp message (same as before)
      payload = {
        phone: response_data['phone'],
        message: response_data['response'],
        conversation_id: response_data['conversation_id'],
        timestamp: response_data['timestamp'],
        source: 'karafka_consumer' # Changed to indicate Karafka
      }
      
      # Send to WhatsApp simulator
      response = HTTParty.post(
        "#{whatsapp_url}/api/receive",
        body: payload.to_json,
        headers: { 'Content-Type' => 'application/json' },
        timeout: 10
      )
      
      if response.success?
        Rails.logger.info "✅ KARAFKA: Response sent to WhatsApp for #{response_data['phone']}"
      else
        Rails.logger.error "❌ KARAFKA: WhatsApp API error: #{response.code} - #{response.body}"
      end
      
    rescue => e
      Rails.logger.error "❌ KARAFKA: Failed to send to WhatsApp: #{e.message}"
    end
  end
end
