# WhatsAppSessionConsumer - Karafka consumer for incoming WhatsApp messages
# Phase 2: Replace MessageWindow with Kafka-based session windowing
class WhatsAppSessionConsumer < Karafka::BaseConsumer
  def consume
    messages.each do |message|
      process_whatsapp_message(message)
    end
  end

  private

  def process_whatsapp_message(message)
    begin
      user_id = message.key
      # Parse JSON manually since we removed deserializer
      message_data = JSON.parse(message.raw_payload)
      
      Rails.logger.info "📱 KARAFKA WINDOWING: Processing message from #{user_id}: #{message_data['content']}"
      
      # Use SessionWindowManager for windowing logic
      session_manager = SessionWindowManager.instance
      session_manager.process_message(user_id, message_data)
      
    rescue => e
      Rails.logger.error "❌ KARAFKA WINDOWING: Error processing message: #{e.message}"
      Rails.logger.error "❌ Message: #{message.raw_payload[0..100]}..."
    end
  end
end
