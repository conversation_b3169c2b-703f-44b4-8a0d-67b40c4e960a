class WhatsappController < ApplicationController
  # Skip CSRF protection for webhook endpoints
  skip_before_action :verify_authenticity_token

  # Webhook verification (for real WhatsApp)
  def verify
    challenge = params['hub.challenge']
    verify_token = params['hub.verify_token']
    
    # In real implementation, verify the token
    if verify_token == ENV['WHATSAPP_VERIFY_TOKEN']
      render plain: challenge
    else
      render plain: 'Forbidden', status: 403
    end
  end
  
  # Receive messages from WhatsApp (or simulator)
  def receive_message
    phone = params[:phone] || params.dig(:entry, 0, :changes, 0, :value, :messages, 0, :from)
    message_content = params[:message] || params.dig(:entry, 0, :changes, 0, :value, :messages, 0, :text, :body)

    unless phone && message_content
      return render json: { error: 'Missing phone or message' }, status: 400
    end

    # Check windowing strategy from params or session
    kafka_windowing = kafka_windowing_enabled?
    windowing_method = kafka_windowing ? 'kafka_state_store' : 'redis_sidekiq'

    Rails.logger.info "📱 Received message from #{phone}: #{message_content}"
    Rails.logger.info "🔄 WINDOWING: #{windowing_method.upcase} strategy selected"

    begin
      # Prepare message data
      message_data = {
        'content' => message_content,
        'timestamp' => Time.current.iso8601,
        'type' => 'text',
        'message_id' => params[:message_id] || SecureRandom.uuid
      }

      if kafka_windowing
        # POC B: Use Karafka windowing
        process_with_kafka_windowing(phone, message_data)
      else
        # POC A: Use traditional windowing
        process_with_traditional_windowing(phone, message_data)
      end

    rescue => e
      Rails.logger.error "Error processing message: #{e.message}"
      render json: { error: 'Failed to process message' }, status: 500
    end
  end

  # API endpoint to get/set windowing strategy
  def windowing_strategy
    if request.get?
      # Get current strategy
      current_strategy = kafka_windowing_enabled? ? 'kafka' : 'traditional'
      render json: {
        windowing_strategy: current_strategy,
        kafka_windowing: kafka_windowing_enabled?
      }
    elsif request.post?
      # Set strategy
      strategy = params[:strategy]
      if strategy == 'kafka'
        session[:kafka_windowing] = 'true'
        Rails.logger.info "🔄 WINDOWING STRATEGY: Switched to Kafka windowing"
      else
        session[:kafka_windowing] = 'false'
        Rails.logger.info "🔄 WINDOWING STRATEGY: Switched to traditional windowing"
      end

      render json: {
        status: 'updated',
        windowing_strategy: strategy,
        kafka_windowing: session[:kafka_windowing] == 'true'
      }
    end
  end

  private

  def kafka_windowing_enabled?
    # Check from params (for API calls) or session (for web interface)
    params[:kafka_windowing] == 'true' ||
    session[:kafka_windowing] == 'true' ||
    ENV['KAFKA_WINDOWING_DEFAULT'] == 'true'
  end

  def process_with_kafka_windowing(phone, message_data)
    Rails.logger.info "🚀 KAFKA WINDOWING: Using SessionWindowManager directly"

    # Use SessionWindowManager directly (no intermediate topic)
    session_manager = SessionWindowManager.instance
    session_manager.process_message(phone, message_data)

    # Get current session count for response
    active_sessions = session_manager.active_sessions_count

    render json: {
      status: 'received',
      phone: phone,
      windowing_method: 'kafka_state_store',
      active_sessions: active_sessions,
      message: 'Message sent to Kafka windowing'
    }

  rescue => e
    Rails.logger.error "❌ KAFKA WINDOWING: #{e.message}"
    render json: { error: 'Windowing error', message: e.message }, status: 500
  end

  def process_with_traditional_windowing(phone, message_data)
    Rails.logger.info "🔄 TRADITIONAL WINDOWING: Using MessageWindow + Sidekiq"

    # Use existing MessageWindow logic
    window = MessageWindow.add_message(phone, message_data)

    render json: {
      status: 'received',
      phone: phone,
      windowing_method: 'redis_sidekiq',
      window_id: window.id,
      message_count: window.message_count
    }
  end

  def create_kafka_producer
    Rdkafka::Config.new(
      "bootstrap.servers" => ENV.fetch('KAFKA_BROKERS', 'kafka:29092'),
      "acks" => "all",
      "retries" => 3
    ).producer
  end
end
