class DashboardController < ApplicationController
  def index
    @active_windows = MessageWindow.active.count
    @total_conversations = Conversation.count
    @recent_conversations = Conversation.recent.limit(10)
    @pending_jobs = 0  # Sidekiq jobs count - placeholder
    
    # Performance metrics
    @avg_response_time = Conversation.where.not(completed_at: nil)
                                   .average('EXTRACT(EPOCH FROM (completed_at - created_at)) * 1000')&.to_i || 0
    
    @success_rate = if Conversation.count > 0
                     (Conversation.where(status: 'completed').count.to_f / Conversation.count * 100).round(1)
                   else
                     0
                   end
  end
  
  def conversations
    @conversations = Conversation.recent
                                .limit(20)
  end
end
