class Api::V1::ConversationsController < Api::V1::BaseController
  def index
    conversations = Conversation.recent.limit(50)
    
    render json: {
      conversations: conversations.map do |conv|
        {
          id: conv.id,
          phone: conv.phone,
          correlation_id: conv.correlation_id,
          status: conv.status,
          message_count: conv.messages&.size || 0,
          response: conv.response,
          duration_ms: conv.duration_ms,
          created_at: conv.created_at,
          completed_at: conv.completed_at
        }
      end
    }
  end
  
  def show
    conversation = Conversation.find(params[:id])
    
    render json: {
      id: conversation.id,
      phone: conversation.phone,
      correlation_id: conversation.correlation_id,
      conversation_id: conversation.conversation_id,
      messages: conversation.messages,
      response: conversation.response,
      status: conversation.status,
      metadata: conversation.metadata,
      duration_ms: conversation.duration_ms,
      created_at: conversation.created_at,
      completed_at: conversation.completed_at,
      error_message: conversation.error_message
    }
  end
end
