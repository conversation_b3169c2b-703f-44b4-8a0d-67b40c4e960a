class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern
  
  protect_from_forgery with: :null_session # For API endpoints
  
  rescue_from StandardError do |exception|
    Rails.logger.error "#{exception.class}: #{exception.message}"
    Rails.logger.error exception.backtrace.join("\n")
    
    if request.path.start_with?('/api/') || request.path.start_with?('/webhook/')
      render json: { error: 'Internal server error' }, status: 500
    else
      render plain: 'Something went wrong', status: 500
    end
  end
end
