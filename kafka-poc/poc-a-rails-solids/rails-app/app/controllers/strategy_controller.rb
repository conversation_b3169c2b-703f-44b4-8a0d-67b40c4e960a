# StrategyController - <PERSON>les dynamic strategy switching
class StrategyController < ApplicationController
  # Get current strategy
  def show
    render json: {
      current_strategy: Rails.application.config.mia_strategy,
      available_strategies: ['http', 'kafka'],
      timestamp: Time.current.iso8601
    }
  end
  
  # Switch strategy dynamically
  def update
    new_strategy = params[:strategy]&.downcase
    
    unless %w[http kafka].include?(new_strategy)
      return render json: { 
        error: 'Invalid strategy. Must be "http" or "kafka"' 
      }, status: 400
    end
    
    old_strategy = Rails.application.config.mia_strategy
    
    # Update the strategy configuration
    Rails.application.config.mia_strategy = new_strategy
    
    # Log the strategy change
    Rails.logger.info "🔄 Strategy changed from #{old_strategy} to #{new_strategy}"
    
    render json: {
      message: "Strategy switched successfully",
      old_strategy: old_strategy,
      new_strategy: new_strategy,
      timestamp: Time.current.iso8601
    }
  end
  
  # Get strategy comparison stats
  def stats
    # Get conversations by strategy from metadata
    http_conversations = Conversation.joins("JOIN json_each(metadata) AS j ON j.key = 'strategy'")
                                   .where("j.value = 'http_direct'")
    
    kafka_conversations = Conversation.joins("JOIN json_each(metadata) AS j ON j.key = 'strategy'")
                                    .where("j.value = 'kafka_async'")
    
    render json: {
      current_strategy: Rails.application.config.mia_strategy,
      comparison: {
        http: {
          total_conversations: http_conversations.count,
          completed: http_conversations.where(status: 'completed').count,
          avg_response_time_ms: http_conversations.where.not(completed_at: nil)
                                                .average('EXTRACT(EPOCH FROM (completed_at - created_at)) * 1000')&.to_i || 0,
          success_rate: calculate_success_rate(http_conversations)
        },
        kafka: {
          total_conversations: kafka_conversations.count,
          completed: kafka_conversations.where(status: 'completed').count,
          avg_response_time_ms: kafka_conversations.where.not(completed_at: nil)
                                                 .average('EXTRACT(EPOCH FROM (completed_at - created_at)) * 1000')&.to_i || 0,
          success_rate: calculate_success_rate(kafka_conversations)
        }
      },
      timestamp: Time.current.iso8601
    }
  end
  
  private
  
  def calculate_success_rate(conversations)
    total = conversations.count
    return 0 if total == 0
    
    completed = conversations.where(status: 'completed').count
    (completed.to_f / total * 100).round(1)
  end
end
