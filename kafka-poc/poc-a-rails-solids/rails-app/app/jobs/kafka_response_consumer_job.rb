# KafkaResponseConsumerJob - Consumes MIA responses from Kafka and sends to WhatsApp
class KafkaResponseConsumerJob < ApplicationJob
  queue_as :default
  
  def perform
    Rails.logger.info "🚀 Starting Kafka Response Consumer..."
    
    begin
      # Create Kafka consumer
      consumer = kafka_consumer
      
      # Subscribe to responses topic
      consumer.subscribe('mia-responses')
      
      Rails.logger.info "✅ Subscribed to 'mia-responses' topic"
      
      # Consume messages
      consumer.each do |message|
        process_response(message)
      rescue => e
        Rails.logger.error "❌ Error in Kafka Response Consumer: #{e.message}"
        break
      end
      
    rescue => e
      Rails.logger.error "❌ Failed to start Kafka Response Consumer: #{e.message}"
      raise
    ensure
      consumer&.close
    end
  end
  
  private
  
  def kafka_consumer
    config = Rdkafka::Config.new(
      "bootstrap.servers": "kafka:29092",
      "group.id": "rails_response_consumer",
      "auto.offset.reset": "latest",
      "enable.partition.eof": false
    )
    config.consumer
  end
  
  def process_response(message)
    begin
      # Parse response data
      response_data = JSON.parse(message.payload)
      phone = message.key
      
      Rails.logger.info "📱 Processing response for #{phone}: #{response_data['response'][0..50]}..."
      
      # Send to WhatsApp simulator
      send_to_whatsapp(response_data)
      
    rescue => e
      Rails.logger.error "❌ Error processing response: #{e.message}"
    end
  end
  
  def send_to_whatsapp(response_data)
    begin
      whatsapp_url = ENV['WHATSAPP_SIMULATOR_URL'] || 'http://whatsapp-simulator:3001'
      
      # Prepare WhatsApp message
      payload = {
        phone: response_data['phone'],
        message: response_data['response'],
        conversation_id: response_data['conversation_id'],
        timestamp: response_data['timestamp'],
        source: 'kafka_async'
      }
      
      # Send to WhatsApp simulator
      response = HTTParty.post(
        "#{whatsapp_url}/api/receive",
        body: payload.to_json,
        headers: { 'Content-Type' => 'application/json' },
        timeout: 10
      )
      
      if response.success?
        Rails.logger.info "✅ Response sent to WhatsApp for #{response_data['phone']}"
      else
        Rails.logger.error "❌ WhatsApp API error: #{response.code} - #{response.body}"
      end
      
    rescue => e
      Rails.logger.error "❌ Failed to send to WhatsApp: #{e.message}"
    end
  end
end
