# SilenceDetectorJob - Detects windows that have been silent for 10+ seconds
# and schedules them for processing (Mercately-style windowing)
class SilenceDetectorJob < ApplicationJob
  queue_as :default
  
  def perform
    # Find windows that have been silent for 10+ seconds and haven't been scheduled yet
    silent_windows = MessageWindow.ready_for_processing

    Rails.logger.info "🔍 Silence detector: Found #{silent_windows.count} windows ready for processing"

    # Process all silent windows directly
    window_ids = silent_windows.pluck(:id)

    if window_ids.any?
      Rails.logger.info "📤 Processing #{window_ids.size} silent windows"

      # Enqueue jobs for all silent windows
      silent_windows.find_each do |window|
        Rails.logger.info "📤 Enqueuing job for window #{window.id} (#{window.phone})"
        ProcessMessageWindowJob.perform_later(window.id)
      end
    end

    # This job is called by the silence detector script, not recurring
  end
  
  private
  
  def time_since_last_message(window)
    ((Time.current - window.last_message_at) / 1.second).round(1)
  end
end
