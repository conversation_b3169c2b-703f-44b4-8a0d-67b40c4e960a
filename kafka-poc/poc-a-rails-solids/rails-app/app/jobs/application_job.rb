class ApplicationJob < ActiveJob::Base
  # Automatically retry jobs that encountered a deadlock
  retry_on ActiveRecord::Deadlocked
  
  # Most jobs are safe to ignore if the underlying records are no longer available
  discard_on ActiveJob::DeserializationError
  
  # Log job execution
  before_perform do |job|
    Rails.logger.info "🚀 Starting job #{job.class} with args: #{job.arguments}"
  end
  
  after_perform do |job|
    Rails.logger.info "✅ Completed job #{job.class}"
  end
  
  rescue_from StandardError do |exception|
    Rails.logger.error "❌ Job #{self.class} failed: #{exception.message}"
    Rails.logger.error exception.backtrace.join("\n")
    raise exception
  end
end
