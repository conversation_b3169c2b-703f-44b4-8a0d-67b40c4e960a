class CleanupExpiredWindowsJob < ApplicationJob
  queue_as :cleanup
  
  def perform
    Rails.logger.info "🧹 Starting cleanup of expired windows"
    
    expired_windows = MessageWindow.expired
    count = expired_windows.count
    
    if count > 0
      Rails.logger.info "🗑️ Found #{count} expired windows to clean up"
      
      expired_windows.find_each do |window|
        Rails.logger.info "🗑️ Cleaning up expired window #{window.id} for #{window.phone}"
        window.destroy!
      end
      
      Rails.logger.info "✅ Cleaned up #{count} expired windows"
    else
      Rails.logger.info "✅ No expired windows to clean up"
    end
  end
end
