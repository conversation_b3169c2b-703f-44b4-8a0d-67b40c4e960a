class ProcessMessageWindowJob < ApplicationJob
  queue_as :default
  
  def perform(window_id)
    window = MessageWindow.find_by(id: window_id)
    
    unless window
      Rails.logger.warn "Window #{window_id} not found, skipping processing"
      return
    end
    
    unless window.ready_for_processing?
      Rails.logger.warn "Window #{window_id} not ready for processing (expired: #{window.expired?}, messages: #{window.messages.size})"
      return
    end
    
    Rails.logger.info "🔄 Processing window #{window_id} for #{window.phone} with #{window.messages.size} messages"
    
    begin
      # Clear job scheduled flag
      window.update_column(:job_scheduled_at, nil)

      # Create conversation record
      conversation = Conversation.create_from_window!(window)

      # Get current strategy from simulator and process with MIA
      current_strategy = MiaProcessingStrategy.current_strategy
      strategy_instance = MiaProcessingStrategy.for(current_strategy)

      Rails.logger.info "🎯 STRATEGY: #{current_strategy.upcase} | Processing conversation #{conversation.id} for #{window.phone}"

      # Process with selected strategy
      strategy_instance.process_conversation(conversation)

      # Clean up window after successful processing
      window.destroy!

      Rails.logger.info "✅ Window #{window_id} processed successfully as conversation #{conversation.id}"
      
    rescue => e
      Rails.logger.error "❌ Failed to process window #{window_id}: #{e.message}"
      # Don't destroy window on error - it will be retried or cleaned up later
      raise e
    end
  end
end
