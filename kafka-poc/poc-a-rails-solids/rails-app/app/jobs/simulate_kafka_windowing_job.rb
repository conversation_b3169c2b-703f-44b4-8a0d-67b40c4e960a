# SimulateKafkaWindowingJob - Temporary job to simulate Karafka windowing processing
# This simulates what Karafka consumers would do until we fix the Karafka setup
class SimulateKafkaWindowingJob < ApplicationJob
  queue_as :default

  def perform(phone, message_data)
    Rails.logger.info "🔄 SIMULATED KARAFKA: Processing message from #{phone}: #{message_data['content']}"
    
    # Simulate the windowing logic that would happen in WhatsAppSessionConsumer
    session_manager = SessionWindowManager.instance
    session_manager.process_message(phone, message_data)
    
    Rails.logger.info "✅ SIMULATED KARAFKA: Message processed successfully"
  end
end
