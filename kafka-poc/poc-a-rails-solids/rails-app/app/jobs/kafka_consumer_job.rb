# KafkaConsumerJob - Consumes messages from <PERSON><PERSON><PERSON> and simulates MIA responses
class KafkaConsumerJob < ApplicationJob
  queue_as :default
  
  def perform
    Rails.logger.info "🚀 Starting Kafka consumer for MIA responses simulation"
    
    consumer = kafka_consumer
    consumer.subscribe('mia-requests')
    
    Rails.logger.info "👂 Listening for messages on 'mia-requests' topic..."
    
    consumer.each do |message|
      begin
        Rails.logger.info "📨 Received Kafka message: #{message.key}"
        
        # Parse the message
        conversation_data = JSON.parse(message.payload)
        correlation_id = conversation_data['correlation_id']
        phone = conversation_data['phone']
        messages = conversation_data['messages']
        
        Rails.logger.info "🤖 Processing MIA request for #{phone} (#{correlation_id})"
        Rails.logger.info "  ↳ Messages: #{messages.map { |m| m['content'] }.join(' + ')}"
        
        # Simulate MIA processing time
        sleep(rand(1..3))
        
        # Generate simulated MIA response
        mia_response = generate_mia_response(messages, phone)
        
        Rails.logger.info "🤖 MIA Response: #{mia_response[0..50]}..."
        
        # Find the conversation and complete it
        conversation = Conversation.find_by(correlation_id: correlation_id)
        
        if conversation
          # Mark conversation as completed with MIA response
          conversation.mark_completed!(mia_response)
          
          # Send WhatsApp response
          whatsapp_service = WhatsappResponseService.new(conversation)
          whatsapp_service.call
          
          Rails.logger.info "✅ Kafka async processing completed for #{phone}"
        else
          Rails.logger.error "❌ Conversation not found for correlation_id: #{correlation_id}"
        end
        
      rescue => e
        Rails.logger.error "❌ Error processing Kafka message: #{e.message}"
        Rails.logger.error "❌ Message: #{message.payload[0..100]}..."
      end
    end
    
  rescue => e
    Rails.logger.error "❌ Kafka consumer error: #{e.message}"
    raise
  ensure
    consumer&.close
  end
  
  private
  
  def kafka_consumer
    config = Rdkafka::Config.new(
      "bootstrap.servers": "kafka:29092",
      "group.id": "mia-consumer-group",
      "auto.offset.reset": "latest",
      "enable.auto.commit": true
    )
    config.consumer
  end
  
  def generate_mia_response(messages, phone)
    # Simulate different types of MIA responses based on message content
    last_message = messages.last['content'].downcase
    
    responses = [
      "¡Hola! Gracias por contactarnos. ¿En qué puedo ayudarte hoy?",
      "Entiendo tu consulta. Déjame revisar esa información para ti.",
      "¡Perfecto! He procesado tu solicitud. ¿Hay algo más en lo que pueda asistirte?",
      "Gracias por tu mensaje. Un agente se pondrá en contacto contigo pronto.",
      "¡Excelente! Tu consulta ha sido registrada exitosamente."
    ]
    
    # Add some context based on the message
    if last_message.include?('hola') || last_message.include?('hello')
      "¡Hola! Bienvenido a nuestro servicio. ¿En qué puedo ayudarte?"
    elsif last_message.include?('test')
      "Mensaje de prueba recibido correctamente. Sistema funcionando via Kafka! 🚀"
    elsif last_message.include?('kafka')
      "¡Genial! El sistema Kafka está funcionando perfectamente. Tu mensaje fue procesado de forma asíncrona."
    else
      responses.sample
    end
  end
end
