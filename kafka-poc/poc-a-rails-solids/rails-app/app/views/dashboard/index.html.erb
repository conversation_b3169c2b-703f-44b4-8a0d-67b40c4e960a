<div class="metrics">
  <div class="metric">
    <div class="metric-value"><%= @active_windows %></div>
    <div class="metric-label">Active Windows</div>
  </div>
  
  <div class="metric">
    <div class="metric-value"><%= @total_conversations %></div>
    <div class="metric-label">Total Conversations</div>
  </div>
  
  <div class="metric">
    <div class="metric-value"><%= @pending_jobs %></div>
    <div class="metric-label">Pending Jobs</div>
  </div>
  
  <div class="metric">
    <div class="metric-value"><%= @avg_response_time %>ms</div>
    <div class="metric-label">Avg Response Time</div>
  </div>
  
  <div class="metric">
    <div class="metric-value"><%= @success_rate %>%</div>
    <div class="metric-label">Success Rate</div>
  </div>
</div>

<h3>Recent Conversations</h3>
<table class="table">
  <thead>
    <tr>
      <th>Phone</th>
      <th>Messages</th>
      <th>Status</th>
      <th>Response Time</th>
      <th>Created</th>
    </tr>
  </thead>
  <tbody>
    <% @recent_conversations.each do |conv| %>
      <tr>
        <td><%= conv.phone %></td>
        <td><%= conv.messages&.size || 0 %></td>
        <td><span class="status status-<%= conv.status %>"><%= conv.status.capitalize %></span></td>
        <td><%= conv.duration_ms ? "#{conv.duration_ms}ms" : "-" %></td>
        <td><%= time_ago_in_words(conv.created_at) %> ago</td>
      </tr>
    <% end %>
  </tbody>
</table>

<script>
  // Auto-refresh every 10 seconds
  setTimeout(() => window.location.reload(), 10000);
</script>
