<!DOCTYPE html>
<html>
  <head>
    <title>POC A - Rails 8 + Redis + Sidekiq</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    
    <style>
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
      .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
      .header { border-bottom: 1px solid #ddd; padding-bottom: 20px; margin-bottom: 20px; }
      .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
      .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
      .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
      .metric-label { font-size: 14px; color: #666; margin-top: 5px; }
      .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
      .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
      .table th { background: #f8f9fa; font-weight: 600; }
      .status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; }
      .status-pending { background: #fff3cd; color: #856404; }
      .status-processing { background: #d1ecf1; color: #0c5460; }
      .status-completed { background: #d4edda; color: #155724; }
      .status-failed { background: #f8d7da; color: #721c24; }
      .nav { margin-bottom: 20px; }
      .nav a { text-decoration: none; padding: 8px 16px; margin-right: 10px; background: #007bff; color: white; border-radius: 4px; }
    </style>
  </head>

  <body>
    <div class="container">
      <div class="header">
        <h1>🚀 POC A: Rails 8 + Redis + Sidekiq</h1>
        <p>Traditional Architecture - PostgreSQL + Redis + Sidekiq Windowing</p>
      </div>
      
      <div class="nav">
        <%= link_to "Dashboard", root_path %>
        <%= link_to "Conversations", conversations_path %>
        <%= link_to "API Docs", "/api/v1/conversations", target: "_blank" %>
      </div>
      
      <%= yield %>
    </div>
  </body>
</html>
