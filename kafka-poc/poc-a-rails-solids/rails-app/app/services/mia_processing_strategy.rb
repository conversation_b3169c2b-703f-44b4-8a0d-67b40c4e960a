# MiaProcessingStrategy - Interface for different MIA processing strategies
class MiaProcessingStrategy
  def self.for(strategy_name)
    case strategy_name.to_s.downcase
    when 'http'
      HttpMiaStrategy.new
    when 'kafka'
      KafkaMiaStrategy.new
    else
      raise ArgumentError, "Unknown MIA strategy: #{strategy_name}. Available: 'http', 'kafka'"
    end
  end

  # Get current strategy from simulator
  def self.current_strategy
    begin
      response = HTTParty.get("#{ENV['WHATSAPP_SIMULATOR_URL']}/api/strategy")
      response.parsed_response['strategy'] || 'http'
    rescue => e
      Rails.logger.warn "⚠️  FALLBACK: Failed to get strategy from simulator: #{e.message}"
      Rails.logger.warn "🔄 FALLBACK: Using HTTP strategy as default"
      'http' # Default fallback
    end
  end
  
  # Interface method - must be implemented by subclasses
  def process_conversation(conversation)
    raise NotImplementedError, "Subclasses must implement #process_conversation"
  end
  
  # Interface method - returns strategy name for logging
  def strategy_name
    raise NotImplementedError, "Subclasses must implement #strategy_name"
  end
end
