class MiaProcessingService
  attr_reader :conversation
  
  def initialize(conversation)
    @conversation = conversation
  end
  
  def call
    conversation.mark_processing!
    
    begin
      # Prepare conversation data for MIA
      mia_request_data = {
        correlation_id: conversation.correlation_id,
        phone: conversation.phone,
        conversation_id: conversation.conversation_id,
        messages: conversation.messages,
        metadata: conversation.metadata
      }
      
      Rails.logger.info "🤖 Sending conversation #{conversation.correlation_id} to MIA"
      
      # Call MIA FastAPI
      response = HTTParty.post(
        "#{ENV['MIA_FASTAPI_URL']}/process_conversation",
        headers: { 'Content-Type' => 'application/json' },
        body: mia_request_data.to_json,
        timeout: 30
      )
      
      if response.success?
        mia_response = response.parsed_response['response']
        
        Rails.logger.info "✅ MIA responded for #{conversation.correlation_id}: #{mia_response[0..100]}..."
        
        conversation.mark_completed!(mia_response)
        
        # Send response back to user
        WhatsappResponseService.new(conversation).call
        
      else
        error_msg = "MIA API error: #{response.code} - #{response.body}"
        Rails.logger.error "❌ #{error_msg}"
        conversation.mark_failed!(error_msg)
      end
      
    rescue => e
      error_msg = "MIA processing failed: #{e.message}"
      Rails.logger.error "❌ #{error_msg}"
      conversation.mark_failed!(error_msg)
      raise e
    end
  end
end
