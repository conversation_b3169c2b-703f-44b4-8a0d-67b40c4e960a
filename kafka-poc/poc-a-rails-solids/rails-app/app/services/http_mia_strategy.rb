# HttpMiaStrategy - Direct HTTP communication with MIA (Strategy A)
class HttpMiaStrategy < MiaProcessingStrategy
  def strategy_name
    'HTTP Direct'
  end
  
  def process_conversation(conversation)
    Rails.logger.info "🌐 HTTP STRATEGY: Processing conversation #{conversation.id} for #{conversation.phone} (SYNCHRONOUS)"
    
    begin
      conversation.mark_processing!
      
      # Prepare request data
      request_data = {
        correlation_id: conversation.correlation_id,
        conversation_id: conversation.correlation_id,
        phone: conversation.phone,
        messages: conversation.messages.map do |msg|
          {
            content: msg['content'],
            timestamp: msg['timestamp'],
            type: msg['type'] || 'text'
          }
        end,
        metadata: {
          strategy: 'http_direct',
          processed_at: Time.current.iso8601
        }
      }
      
      Rails.logger.info "🤖 MIA CONTEXT: #{conversation.correlation_id} -> Responding to #{conversation.messages.size} messages: #{conversation.phone}"
      
      # Send to MIA via HTTP
      response = HTTParty.post(
        'http://mia-fastapi:8000/process_conversation',
        headers: { 'Content-Type' => 'application/json' },
        body: request_data.to_json,
        timeout: 30
      )
      
      if response.success?
        mia_response = response.parsed_response['response']
        
        Rails.logger.info "✅ MIA responded for #{conversation.correlation_id}: #{mia_response[0..50]}..."
        Rails.logger.info "🤖 MIA RESPONDING TO: #{conversation.phone}"
        Rails.logger.info "  ↳ Original messages (#{conversation.messages.size}): #{conversation.messages.map { |m| m['content'] }.join(' + ')}"
        Rails.logger.info "📤 Sending response to #{conversation.phone}: #{mia_response[0..50]}..."
        Rails.logger.info "📱 WHATSAPP SEND: #{conversation.phone} -> #{mia_response}"
        Rails.logger.info "  ↳ In response to: #{conversation.messages.map { |m| m['content'] }.join(' + ')}"
        
        # Mark conversation as completed with MIA response
        conversation.mark_completed!(mia_response)

        # Send WhatsApp response
        whatsapp_service = WhatsappResponseService.new(conversation)
        whatsapp_service.call

        Rails.logger.info "✅ HTTP strategy completed for #{conversation.phone}"
        
      else
        error_msg = "MIA HTTP error: #{response.code} - #{response.body}"
        Rails.logger.error "❌ MIA request failed: #{error_msg}"
        conversation.mark_failed!(error_msg)
      end
      
    rescue => e
      Rails.logger.error "❌ HTTP strategy error for conversation #{conversation.id}: #{e.message}"
      conversation.mark_failed!(e.message)
      raise
    end
  end
end
