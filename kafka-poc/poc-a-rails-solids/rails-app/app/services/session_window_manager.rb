# SessionWindowManager - Core windowing logic using Kafka state store
# Phase 2: Session windowing with Kafka persistence
require 'kafka'

class SessionWindowManager
  include Singleton
  
  WINDOW_DURATION = 10.seconds
  
  def initialize
    @state_store = KafkaStateStore.new('whatsapp_sessions_state')
    @producer = create_producer
    @logger = Rails.logger
    
    # Start expiration checker thread
    start_expiration_checker
    
    @logger.info "🔄 EVOLUTION: SessionWindowManager initialized with Kafka state store"
  end

  def process_message(user_id, message_data)
    current_time = Time.now
    existing_session = @state_store.get(user_id)

    if existing_session && session_active?(existing_session, current_time)
      extend_session(user_id, existing_session, message_data, current_time)
    else
      create_session(user_id, message_data, current_time)
    end
  end

  def check_and_close_if_expired(user_id)
    session = @state_store.get(user_id)
    return unless session
    
    if Time.now >= Time.parse(session['expires_at'])
      close_expired_session(user_id, session)
    end
  end

  def active_sessions_count
    @state_store.session_count
  end

  private

  def extend_session(user_id, session, message, current_time)
    session['messages'] << message
    session['last_activity'] = current_time.iso8601
    session['expires_at'] = (current_time + WINDOW_DURATION).iso8601

    @state_store.put(user_id, session)
    
    @logger.info "🔄 KAFKA WINDOWING: Extended session #{user_id}: #{session['messages'].size} messages"
  end

  def create_session(user_id, message, current_time)
    new_session = {
      'user_id' => user_id,
      'messages' => [message],
      'window_start' => current_time.iso8601,
      'last_activity' => current_time.iso8601,
      'expires_at' => (current_time + WINDOW_DURATION).iso8601
    }

    @state_store.put(user_id, new_session)
    
    @logger.info "🔄 KAFKA WINDOWING: Created new session for #{user_id}"
  end

  def session_active?(session, current_time)
    Time.parse(session['expires_at']) > current_time
  end

  def close_expired_session(user_id, session)
    # Create aggregated event (same format as MessageWindow)
    aggregated_event = {
      user_id: user_id,
      messages: session['messages'],
      message_count: session['messages'].size,
      window_start: session['window_start'],
      window_end: session['last_activity'],
      closed_at: Time.now.iso8601,
      windowing_method: 'kafka_state_store'
    }

    # Produce aggregated event for MIA processing using ruby-kafka
    @producer.produce(
      aggregated_event.to_json,
      topic: 'whatsapp_aggregated_events',
      key: user_id
    )
    @producer.deliver_messages

    # Remove session from state store
    @state_store.delete(user_id)
    
    @logger.info "🔄 KAFKA WINDOWING: Closed session #{user_id}: #{session['messages'].size} messages"
  end

  def start_expiration_checker
    Thread.new do
      loop do
        sleep(1)
        check_immediate_expirations
      end
    end
  end

  def check_immediate_expirations
    current_time = Time.now
    
    @state_store.scan_all_sessions do |user_id, session|
      expires_at = Time.parse(session['expires_at'])
      
      if current_time >= expires_at
        close_expired_session(user_id, session)
      end
    end
  rescue => e
    @logger.error "❌ KAFKA WINDOWING: Error checking expirations: #{e.message}"
  end

  def create_producer
    # Use ruby-kafka for more reliable producing
    @kafka_client = Kafka.new(
      seed_brokers: [ENV.fetch('KAFKA_BROKERS', 'kafka:29092')],
      client_id: 'session-window-manager'
    )
    @kafka_client.producer
  end
end
