# KafkaMiaStrategy - Kafka-based communication with MIA (Strategy B)
class KafkaMiaStrategy < MiaProcessingStrategy
  def strategy_name
    'Kafka Async'
  end
  
  def process_conversation(conversation)
    Rails.logger.info "🚀 KAFKA STRATEGY: Processing conversation #{conversation.id} for #{conversation.phone} (ASYNCHRONOUS)"
    
    begin
      conversation.mark_processing!
      
      # Prepare conversation data for Kafka
      conversation_data = {
        conversation_id: conversation.correlation_id,
        phone: conversation.phone,
        messages: conversation.messages.map do |msg|
          {
            content: msg['content'],
            timestamp: msg['timestamp'],
            type: msg['type'] || 'text'
          }
        end,
        metadata: {
          strategy: 'kafka_async',
          sent_to_kafka_at: Time.current.iso8601,
          rails_conversation_id: conversation.id
        }
      }
      
      Rails.logger.info "🤖 MIA CONTEXT: #{conversation.correlation_id} -> Publishing to Kafka #{conversation.messages.size} messages: #{conversation.phone}"
      
      # Publish to Kafka
      kafka_service = KafkaPublishService.new
      result = kafka_service.publish_conversation(conversation_data)
      
      if result[:success]
        Rails.logger.info "✅ Conversation #{conversation.correlation_id} published to Kafka successfully"
        Rails.logger.info "📤 Kafka Topic: #{result[:topic]}, Partition: #{result[:partition]}, Offset: #{result[:offset]}"
        
        # Mark as sent to Kafka (will be completed when response comes back)
        conversation.update!(
          status: 'sent_to_kafka',
          metadata: conversation.metadata.merge(
            kafka_topic: result[:topic],
            kafka_partition: result[:partition],
            kafka_offset: result[:offset],
            sent_to_kafka_at: Time.current.iso8601
          )
        )
        
        Rails.logger.info "🔄 Conversation #{conversation.correlation_id} waiting for MIA response via Kafka..."
        
      else
        error_msg = "Kafka publish failed: #{result[:error]}"
        Rails.logger.error "❌ #{error_msg}"
        conversation.mark_failed!(error_msg)
        raise error_msg
      end
      
    rescue => e
      Rails.logger.error "❌ Kafka strategy error for conversation #{conversation.id}: #{e.message}"
      conversation.mark_failed!(e.message)
      raise
    ensure
      kafka_service&.close
    end
  end
end
