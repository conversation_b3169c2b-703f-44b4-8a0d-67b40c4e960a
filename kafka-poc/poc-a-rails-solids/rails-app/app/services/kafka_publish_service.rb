# KafkaPublishService - Publishes conversations to Kafka for MIA processing
require 'rdkafka'

class KafkaPublishService
  attr_reader :producer
  
  def initialize
    @producer = Rdkafka::Config.new(
      "bootstrap.servers" => "kafka:29092",
      "acks" => "all",
      "retries" => 3,
      "retry.backoff.ms" => 100
    ).producer
  end
  
  def publish_conversation(conversation_data)
    begin
      Rails.logger.info "🚀 Publishing conversation #{conversation_data[:conversation_id]} to <PERSON><PERSON><PERSON> for #{conversation_data[:phone]}"
      
      # Publish to conversations topic (for MIA)
      delivery_report = @producer.produce(
        topic: 'mia-requests',
        key: conversation_data[:phone],
        payload: conversation_data.to_json,
        headers: {
          'conversation_id' => conversation_data[:conversation_id],
          'source' => 'rails_windowing',
          'timestamp' => Time.current.to_i.to_s,
          'strategy' => 'kafka_async'
        }
      )
      
      # Wait for delivery confirmation
      delivery_report.wait(max_wait_timeout: 5)
      
      Rails.logger.info "✅ Conversation published to <PERSON><PERSON><PERSON> successfully"

      # Wait for delivery and get metadata
      delivery_result = delivery_report.wait(max_wait_timeout: 5)

      {
        success: true,
        topic: 'mia-requests',
        partition: delivery_result&.partition || 0,
        offset: delivery_result&.offset || 0,
        conversation_id: conversation_data[:conversation_id]
      }
      
    rescue => e
      Rails.logger.error "❌ Failed to publish conversation to Kafka: #{e.message}"
      
      {
        success: false,
        error: e.message
      }
    end
  end
  
  def close
    @producer&.close
  end
end
