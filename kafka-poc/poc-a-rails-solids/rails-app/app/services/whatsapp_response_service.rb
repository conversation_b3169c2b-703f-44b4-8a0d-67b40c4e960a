class WhatsappResponseService
  attr_reader :conversation
  
  def initialize(conversation)
    @conversation = conversation
  end
  
  def call
    return unless conversation.response.present?

    # Extract original messages for context
    original_messages = conversation.messages.map { |msg| msg['content'] }.join(' + ')
    message_count = conversation.messages.size

    Rails.logger.info "🤖 MIA RESPONDING TO: #{conversation.phone}"
    Rails.logger.info "  ↳ Original messages (#{message_count}): #{original_messages}"
    Rails.logger.info "📤 Sending response to #{conversation.phone}: #{conversation.response[0..50]}..."

    begin
      # In real implementation, this would use WhatsApp Business API
      # For POC, we'll use a simple HTTP call to our simulator

      response_data = {
        phone: conversation.phone,
        message: conversation.response,
        original_messages: original_messages,
        message_count: message_count,
        metadata: {
          correlation_id: conversation.correlation_id,
          conversation_id: conversation.conversation_id,
          timestamp: Time.current.iso8601
        }
      }

      # For POC: log the response with context (in production this would be WhatsApp API)
      Rails.logger.info "📱 WHATSAPP SEND: #{conversation.phone} -> #{conversation.response}"
      Rails.logger.info "  ↳ In response to: #{original_messages}"
      
      # If running with simulator, try to send response
      if ENV['WHATSAPP_SIMULATOR_URL']
        HTTParty.post(
          "#{ENV['WHATSAPP_SIMULATOR_URL']}/receive_response",
          headers: { 'Content-Type' => 'application/json' },
          body: response_data.to_json,
          timeout: 5
        )
      end
      
      Rails.logger.info "✅ Response sent successfully to #{conversation.phone}"
      
    rescue => e
      Rails.logger.error "❌ Failed to send response to #{conversation.phone}: #{e.message}"
      # Don't fail the whole conversation for response sending errors
    end
  end
end
