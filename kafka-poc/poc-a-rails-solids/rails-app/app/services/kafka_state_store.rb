# KafkaStateStore - Kafka-backed state persistence for session windowing
# Phase 2: Replace Redis with Kafka compacted topic for state storage
class KafkaStateStore
  def initialize(topic_name)
    @topic_name = topic_name
    @memory_cache = Concurrent::Hash.new
    @producer = create_producer
    @logger = Rails.logger
    
    # Restore state from Kafka on startup (disabled for now to speed up startup)
    # restore_state_on_startup
    
    @logger.info "🔄 EVOLUTION: KafkaStateStore initialized for topic #{topic_name}"
  end

  def put(key, value)
    # Update in-memory cache first (for fast reads)
    @memory_cache[key] = value
    
    # Persist to Kafka (for durability across deploys)
    @producer.produce(
      topic: @topic_name,
      key: key,
      payload: value.to_json
    )
    
    @logger.debug "📝 KAFKA STATE: Stored session #{key}"
  end

  def get(key)
    @memory_cache[key]
  end

  def delete(key)
    @memory_cache.delete(key)
    
    # Send tombstone record (null payload) for deletion
    @producer.produce(
      topic: @topic_name,
      key: key,
      payload: nil
    )
    
    @logger.debug "🗑️ KAFKA STATE: Deleted session #{key}"
  end

  def scan_all_sessions(&block)
    @memory_cache.each(&block)
  end

  def session_count
    @memory_cache.size
  end

  private

  def restore_state_on_startup
    @logger.info "🔄 KAFKA STATE: Restoring session state from #{@topic_name}..."
    
    begin
      # Create a temporary consumer to read the entire topic
      consumer = create_restore_consumer
      consumer.subscribe(@topic_name)
      
      restored_count = 0
      
      # Read all messages from the beginning
      consumer.each do |message|
        if message.payload.nil?
          # Tombstone record - delete from cache
          @memory_cache.delete(message.key)
        else
          # Regular record - restore to cache
          session_data = JSON.parse(message.payload)
          @memory_cache[message.key] = session_data
          restored_count += 1
        end
        
        # Break if we've caught up (this is a simplified approach)
        break if caught_up_with_latest?
      end
      
      consumer.close
      @logger.info "✅ KAFKA STATE: Restored #{restored_count} active sessions"
      
    rescue => e
      @logger.error "❌ KAFKA STATE: Failed to restore state: #{e.message}"
      # Continue with empty state if restoration fails
    end
  end

  def create_producer
    Rdkafka::Config.new(
      "bootstrap.servers" => ENV.fetch('KAFKA_BROKERS', 'kafka:29092'),
      "acks" => "all",
      "retries" => 3,
      "retry.backoff.ms" => 100
    ).producer
  end

  def create_restore_consumer
    # Use a unique group ID for state restoration
    restore_group_id = "state-restore-#{SecureRandom.hex(4)}"
    
    Rdkafka::Config.new(
      "bootstrap.servers" => ENV.fetch('KAFKA_BROKERS', 'kafka:29092'),
      "group.id" => restore_group_id,
      "auto.offset.reset" => "earliest", # Read from beginning
      "enable.auto.commit" => false
    ).consumer
  end

  def caught_up_with_latest?
    # Simplified approach: assume we've caught up after reading for a bit
    # In production, you'd check watermarks or use a timeout
    @restore_start_time ||= Time.now
    Time.now - @restore_start_time > 5.seconds
  end
end
