# MessageWindow - Manages windowing logic using PostgreSQL
class MessageWindow < ApplicationRecord
  # No need for serialize with JSON column type in Rails 8
  
  WINDOW_DURATION = 10.seconds
  
  validates :phone, presence: true
  # Allow empty messages array initially
  
  scope :active, -> { where('expires_at > ?', Time.current) }
  scope :expired, -> { where('expires_at <= ?', Time.current) }
  scope :ready_for_processing, -> { where('last_message_at <= ?', WINDOW_DURATION.ago) }
  
  def self.add_message(phone, message_data)
    window = find_or_create_active_window(phone)
    window.add_message!(message_data)
    # NO schedule job here - will be handled by silence detector
    window
  end
  
  def self.find_or_create_active_window(phone)
    # Find existing active window for this phone (not expired, regardless of job status)
    existing_window = where(phone: phone)
                        .where('expires_at > ?', Time.current)
                        .order(last_message_at: :desc)
                        .first

    if existing_window
      Rails.logger.info "🔄 REUSING WINDOW: #{phone} - Window ID #{existing_window.id}"
      existing_window
    else
      Rails.logger.info "🆕 NEW WINDOW: #{phone}"
      create!(
        phone: phone,
        messages: [],
        expires_at: WINDOW_DURATION.from_now,
        last_message_at: Time.current
      )
    end
  end
  
  def add_message!(message_data)
    Rails.logger.info "📝 ADDING MESSAGE: #{phone} - Window ID #{id} - Message: #{message_data['content']}"

    # Add message to window
    self.messages << {
      content: message_data['content'],
      timestamp: message_data['timestamp'] || Time.current.iso8601,
      type: message_data['type'] || 'text',
      message_id: message_data['message_id'] || SecureRandom.uuid
    }

    # Update window metadata - THIS RESETS THE TIMER
    old_time = self.last_message_at
    self.last_message_at = Time.current
    self.expires_at = WINDOW_DURATION.from_now
    self.message_count = messages.size

    Rails.logger.info "⏰ TIMER RESET: #{phone} - Old: #{old_time} -> New: #{self.last_message_at} (#{messages.size} messages)"

    save!
  end
  
  def schedule_processing!
    Rails.logger.info "⏰ SCHEDULING PROCESSING: Window #{id} for #{phone} in #{WINDOW_DURATION} seconds"

    # Always schedule a new job (Sidekiq will handle duplicates)
    # The job will check if the window is still ready when it executes
    ProcessMessageWindowJob.set(wait: WINDOW_DURATION).perform_later(id)
    update_column(:job_scheduled_at, Time.current)
  end

  def job_scheduled?
    # Check if a job was scheduled recently (within the window duration)
    job_scheduled_at.present? && job_scheduled_at > WINDOW_DURATION.ago
  end
  
  def ready_for_processing?
    expired? && messages.present?
  end
  
  def expired?
    expires_at <= Time.current
  end
  
  def to_conversation_data
    {
      correlation_id: SecureRandom.uuid,
      phone: phone,
      conversation_id: "conv_#{phone.gsub(/[^\d]/, '')}_#{created_at.strftime('%Y%m%d_%H%M%S')}",
      messages: messages,
      metadata: {
        window_closed_at: Time.current.iso8601,
        message_count: message_count,
        window_duration: (Time.current - created_at).to_i
      }
    }
  end
end
