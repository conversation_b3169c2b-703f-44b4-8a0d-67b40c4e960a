# Conversation - Stores processed conversations and responses
class Conversation < ApplicationRecord
  validates :phone, presence: true
  validates :correlation_id, presence: true, uniqueness: true
  validates :status, inclusion: { in: %w[pending processing completed failed sent_to_kafka] }
  
  scope :recent, -> { order(created_at: :desc) }
  scope :by_phone, ->(phone) { where(phone: phone) }
  
  def self.create_from_window!(window)
    conversation_data = window.to_conversation_data
    
    create!(
      phone: conversation_data[:phone],
      correlation_id: conversation_data[:correlation_id],
      conversation_id: conversation_data[:conversation_id],
      messages: conversation_data[:messages],
      metadata: conversation_data[:metadata],
      status: 'pending'
    )
  end
  
  def mark_processing!
    update!(status: 'processing', processed_at: Time.current)
  end
  
  def mark_completed!(response)
    update!(
      status: 'completed',
      response: response,
      completed_at: Time.current
    )
  end
  
  def mark_failed!(error_message)
    update!(
      status: 'failed',
      error_message: error_message,
      failed_at: Time.current
    )
  end
  
  def duration_ms
    return nil unless completed_at && created_at
    ((completed_at - created_at) * 1000).to_i
  end
end
