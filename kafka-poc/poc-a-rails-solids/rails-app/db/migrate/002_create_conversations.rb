class CreateConversations < ActiveRecord::Migration[8.0]
  def change
    create_table :conversations do |t|
      t.string :phone, null: false, index: true
      t.string :correlation_id, null: false, index: { unique: true }
      t.string :conversation_id, null: false
      t.json :messages, null: false, default: []
      t.text :response
      t.string :status, null: false, default: 'pending', index: true
      t.json :metadata, default: {}
      t.text :error_message
      t.datetime :processed_at
      t.datetime :completed_at
      t.datetime :failed_at
      
      t.timestamps
    end
    
    add_index :conversations, [:phone, :created_at]
    add_index :conversations, :created_at
  end
end
