class CreateMessageWindows < ActiveRecord::Migration[8.0]
  def change
    create_table :message_windows do |t|
      t.string :phone, null: false
      t.json :messages, null: false, default: []
      t.integer :message_count, default: 0
      t.datetime :expires_at, null: false
      t.datetime :last_message_at, null: false

      t.timestamps
    end

    add_index :message_windows, :phone
    add_index :message_windows, [:phone, :expires_at]
    add_index :message_windows, :expires_at
  end
end
