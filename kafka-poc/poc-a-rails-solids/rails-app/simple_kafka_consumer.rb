#!/usr/bin/env ruby

# Simple Kafka Consumer - Alternative to Karafka
# Consumes from whatsapp_messages and does windowing

require 'bundler/setup'
require 'rdkafka'
require 'json'
require 'redis'

# Check if enabled
if ENV['KARAFKA_ENABLED'] != 'true'
  puts "Simple Kafka Consumer disabled (KARAFKA_ENABLED != 'true')"
  exit 0
end

puts "🚀 Starting Simple Kafka Consumer..."

# Initialize Redis for windowing
redis = Redis.new(url: ENV.fetch('REDIS_URL', 'redis://redis:6379/0'))

# Initialize Kafka consumer
config = Rdkafka::Config.new(
  'bootstrap.servers' => ENV.fetch('KAFKA_BROKERS', 'kafka:29092'),
  'group.id' => 'simple-windowing-consumer',
  'auto.offset.reset' => 'latest',
  'enable.auto.commit' => true
)

consumer = config.consumer
producer = config.producer

puts "📋 Subscribing to whatsapp_messages..."
consumer.subscribe('whatsapp_messages')

puts "✅ Simple Kafka Consumer started successfully"

# Simple windowing logic
windows = {}

begin
  consumer.each do |message|
    begin
      puts "📱 Received message: #{message.payload[0..50]}..."
      
      # Parse message
      message_data = JSON.parse(message.payload)
      user_id = message.key
      
      puts "🔄 Processing message from #{user_id}: #{message_data['content']}"
      
      # Simple windowing: collect messages for 10 seconds
      windows[user_id] ||= []
      windows[user_id] << message_data
      
      # Set expiration for window (10 seconds)
      redis.setex("window:#{user_id}", 10, "active")
      
      # Check if window should close (simple approach)
      sleep(0.1) # Small delay
      if !redis.exists("window:#{user_id}")
        # Window closed, process messages
        messages = windows[user_id]
        windows.delete(user_id)
        
        puts "🪟 Window closed for #{user_id}, processing #{messages.size} messages"
        
        # Create aggregated event (same format as Karafka windowing)
        aggregated_event = {
          user_id: user_id,
          messages: messages,
          message_count: messages.size,
          window_start: Time.current.iso8601,
          window_end: Time.current.iso8601,
          closed_at: Time.current.iso8601,
          windowing_method: 'simple_kafka_consumer'
        }
        
        # Publish to whatsapp_aggregated_events
        producer.produce(
          topic: 'whatsapp_aggregated_events',
          key: user_id,
          payload: aggregated_event.to_json
        )
        
        puts "📤 Published aggregated event for #{user_id}"
      end
      
    rescue => e
      puts "❌ Error processing message: #{e.message}"
    end
  end
rescue Interrupt
  puts "🛑 Shutting down Simple Kafka Consumer..."
ensure
  consumer&.close
  producer&.close
  redis&.close
end
