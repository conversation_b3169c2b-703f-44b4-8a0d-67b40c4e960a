require 'sidekiq/web'

Rails.application.routes.draw do
  # Sidekiq Web UI
  mount Sidekiq::Web => '/sidekiq'

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check
  
  # WhatsApp webhook simulation
  post '/webhook/whatsapp', to: 'whatsapp#receive_message'
  get '/webhook/whatsapp', to: 'whatsapp#verify'

  # Windowing strategy toggle (for simulator)
  get '/api/windowing_strategy', to: 'whatsapp#windowing_strategy'
  post '/api/windowing_strategy', to: 'whatsapp#windowing_strategy'

  # Kafka metrics for dashboard
  get '/api/kafka/metrics', to: 'kafka_metrics#index'
  
  # API endpoints
  namespace :api do
    namespace :v1 do
      resources :message_windows, only: [:index, :show]
      resources :conversations, only: [:index, :show]
    end
  end
  
  # Web interface for monitoring
  root 'dashboard#index'
  get '/dashboard', to: 'dashboard#index'
  get '/conversations', to: 'dashboard#conversations'
end
