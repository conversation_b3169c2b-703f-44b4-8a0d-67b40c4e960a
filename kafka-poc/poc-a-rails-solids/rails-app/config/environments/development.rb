require "active_support/core_ext/integer/time"

Rails.application.configure do
  config.enable_reloading = true
  config.eager_load = false
  config.consider_all_requests_local = true
  config.server_timing = true
  
  # Caching
  config.cache_classes = false
  config.cache_store = :memory_store

  # Active Job - use Sidekiq
  config.active_job.queue_adapter = :sidekiq
  
  # Logging
  config.log_level = :info
  config.log_tags = [ :request_id ]
  
  # Assets
  config.assets.debug = true
  config.assets.quiet = true
  
  # Action Mailer
  config.action_mailer.raise_delivery_errors = false
  config.action_mailer.perform_caching = false
  
  # Active Record
  config.active_record.migration_error = :page_load
  config.active_record.verbose_query_logs = true
  
  # Hosts
  config.hosts << "rails-app"
  config.hosts << "localhost"
  
  # CORS for API endpoints
  config.middleware.insert_before 0, Rack::Cors do
    allow do
      origins '*'
      resource '*', headers: :any, methods: [:get, :post, :put, :patch, :delete, :options, :head]
    end
  end
end
