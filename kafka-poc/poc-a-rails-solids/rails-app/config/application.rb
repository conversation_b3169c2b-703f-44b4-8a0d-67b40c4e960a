require_relative "boot"

require "rails/all"

Bundler.require(*Rails.groups)

module MercatelyPocA
  class Application < Rails::Application
    config.load_defaults 8.0
    
    # ActiveJob configuration - use Sidekiq
    config.active_job.queue_adapter = :sidekiq

    # Time zone
    config.time_zone = 'America/Argentina/Buenos_Aires'
    
    # API-only for webhook endpoints
    config.api_only = false
  end
end
