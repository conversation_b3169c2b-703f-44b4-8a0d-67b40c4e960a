# Karafka configuration for POC B
# Only define KarafkaApp if Karafka is enabled
if ENV['KARAFKA_ENABLED'] == 'true'
  require 'karafka'

  # Load consumer classes after Karafka is loaded
  require_relative '../app/consumers/mia_response_consumer'
  require_relative '../app/consumers/whats_app_session_consumer'
  require_relative '../app/consumers/aggregated_events_consumer'

  class KarafkaApp < Karafka::App
    setup do |config|
      config.kafka = {
        'bootstrap.servers': ENV.fetch('KAFKA_BROKERS', 'kafka:29092'),
        'group.id': 'rails-karafka-consumers',
        'auto.offset.reset': 'earliest',
        'enable.auto.commit': true,
        'session.timeout.ms': 10000,
        'heartbeat.interval.ms': 3000,
        'max.poll.interval.ms': 30000,
        'fetch.max.wait.ms': 500
      }

      config.client_id = 'rails-karafka-app'
      config.consumer_persistence = false
      config.max_wait_time = 500
      config.shutdown_timeout = 10_000
    end

    # Consumer routes - simplified for testing
    routes.draw do
      topic 'whatsapp_aggregated_events' do
        consumer AggregatedEventsConsumer
      end
    end
  end

end # End of KARAFKA_ENABLED check
