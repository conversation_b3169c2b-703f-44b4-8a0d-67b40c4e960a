#!/usr/bin/env ruby

# Karafka boot file for POC B windowing evolution
ENV['BUNDLE_GEMFILE'] ||= File.expand_path('../Gemfile', __FILE__)

require 'bundler/setup'

# Check if Karafka is enabled
if ENV['KARAFKA_ENABLED'] != 'true'
  puts "Karafka disabled (KARAFKA_ENABLED != 'true')"
  puts "To enable Karafka, set KARAFKA_ENABLED=true"
  # Only exit if this file is being run directly (not loaded by Rails)
  exit 0 if __FILE__ == $0
end

# Only run Karafka if this file is executed directly
if __FILE__ == $0
  puts "🔧 Loading Karafka configuration from config/karafka.rb..."

  # Load Karafka configuration directly
  require_relative 'config/karafka'

  # Boot Karafka
  if defined?(KarafkaApp)
    puts "🚀 Starting KarafkaApp..."

    topics = []
    KarafkaApp.routes.each do |route|
      route.topics.each { |topic| topics << topic.name }
    end
    puts "🎯 Topics configured: #{topics.join(', ')}"
    puts "🔗 Kafka brokers: #{ENV['KAFKA_BROKERS']}"

    KarafkaApp.run!
  else
    puts "❌ KarafkaApp not defined, check config/karafka.rb"
    exit 1
  end
end
