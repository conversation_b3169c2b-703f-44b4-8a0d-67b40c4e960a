#!/usr/bin/env ruby

# Ruby Kafka Consumer - Simple alternative to Karafka
# Consumes from whatsapp_messages and does windowing

require 'bundler/setup'
require 'kafka'
require 'json'
require 'redis'

# Check if enabled
if ENV['KARAFKA_ENABLED'] != 'true'
  puts "Ruby Kafka Consumer disabled (KARAFKA_ENABLED != 'true')"
  exit 0
end

puts "🚀 Starting Ruby Kafka Consumer..."

# Initialize Redis for windowing state
redis = Redis.new(url: ENV.fetch('REDIS_URL', 'redis://redis:6379/0'))

# Initialize Kafka client
kafka = Kafka.new(
  seed_brokers: [ENV.fetch('KAFKA_BROKERS', 'kafka:29092')],
  client_id: 'ruby-kafka-windowing-consumer'
)

# Create consumer
consumer = kafka.consumer(group_id: 'ruby-kafka-windowing-group')

# Subscribe to topics
consumer.subscribe('whatsapp_messages')

puts "📋 Subscribed to whatsapp_messages"
puts "✅ Ruby Kafka Consumer started successfully"

# Simple windowing logic using Redis
def process_windowing(message_data, user_id, redis, kafka)
  puts "🔄 Processing message from #{user_id}: #{message_data['content']}"
  
  # Add message to window
  window_key = "window:#{user_id}"
  redis.lpush(window_key, message_data.to_json)
  redis.expire(window_key, 15) # 15 seconds window
  
  # Schedule window close job (simple approach with Redis)
  close_key = "close:#{user_id}"
  redis.setex(close_key, 10, Time.current.to_i) # Close after 10 seconds
  
  # Check if we should close the window
  sleep(0.1) # Small delay to allow for more messages
  
  # If close key expired, process the window
  unless redis.exists?(close_key)
    messages_json = redis.lrange(window_key, 0, -1)
    redis.del(window_key)
    
    if messages_json.any?
      messages = messages_json.map { |json| JSON.parse(json) }
      puts "🪟 Window closed for #{user_id}, processing #{messages.size} messages"
      
      # Create aggregated event
      aggregated_event = {
        user_id: user_id,
        messages: messages.reverse, # Reverse to get chronological order
        message_count: messages.size,
        window_start: Time.current.iso8601,
        window_end: Time.current.iso8601,
        closed_at: Time.current.iso8601,
        windowing_method: 'ruby_kafka_consumer'
      }
      
      # Publish to whatsapp_aggregated_events
      producer = kafka.producer
      producer.produce(
        aggregated_event.to_json,
        topic: 'whatsapp_aggregated_events',
        key: user_id
      )
      producer.deliver_messages
      producer.shutdown
      
      puts "📤 Published aggregated event for #{user_id}"
    end
  end
end

# Main consumer loop
begin
  consumer.each_message do |message|
    begin
      puts "📱 Received message: #{message.value[0..50]}..."
      
      # Parse message
      message_data = JSON.parse(message.value)
      user_id = message.key
      
      # Process windowing
      process_windowing(message_data, user_id, redis, kafka)
      
    rescue => e
      puts "❌ Error processing message: #{e.message}"
      puts e.backtrace.first(3)
    end
  end
rescue Interrupt
  puts "🛑 Shutting down Ruby Kafka Consumer..."
ensure
  consumer&.stop
  kafka&.close
  redis&.close
end
