#!/usr/bin/env ruby

# Simple Windowing Consumer - Sin Rails, solo ruby-kafka
# Procesa eventos agregados y los envía a mia-requests

require 'bundler/setup'
require 'kafka'
require 'json'
require 'time'
require 'net/http'
require 'uri'

puts "🚀 Starting Simple Windowing Consumer (No Rails)..."

# Check if enabled
if ENV['KARAFKA_ENABLED'] != 'true'
  puts "Consumer disabled (KARAFKA_ENABLED != 'true')"
  exit 0
end

# Kafka configuration
kafka = Kafka.new(
  seed_brokers: [ENV.fetch('KAFKA_BROKERS', 'kafka:29092')],
  client_id: 'simple-windowing-consumer'
)

puts "✅ Kafka client created"

# Create consumer and producer
consumer = kafka.consumer(group_id: 'simple-windowing-group')
producer = kafka.producer

# Subscribe to aggregated events
consumer.subscribe('whatsapp_aggregated_events')
puts "📋 Subscribed to whatsapp_aggregated_events"

def process_aggregated_event(message, producer)
  begin
    # Parse the event data
    event_data = JSON.parse(message.value)
    user_id = message.key
    
    puts "🔄 Processing aggregated event for #{user_id} (#{event_data['message_count']} messages)"
    
    # Create conversation data for MIA (simplified, no Rails models)
    conversation_id = "simple-#{Time.now.to_i}-#{user_id}"
    
    message_data = {
      conversation_id: conversation_id,
      phone: user_id,
      messages: event_data['messages'],
      metadata: {
        strategy: 'simple_windowing_consumer',
        sent_to_kafka_at: Time.now.strftime('%Y-%m-%dT%H:%M:%SZ'),
        windowing_method: 'kafka_state_store',
        message_count: event_data['message_count'],
        processed_by: 'simple_ruby_consumer'
      }
    }
    
    # Publish to mia-requests
    producer.produce(
      message_data.to_json,
      topic: 'mia-requests',
      key: user_id
    )
    producer.deliver_messages
    
    puts "📤 Published to mia-requests: #{conversation_id}"
    return true
    
  rescue => e
    puts "❌ Error processing event: #{e.message}"
    puts e.backtrace.first(3)
    return false
  end
end

puts "🔄 Starting consumer loop..."

begin
  # Main consumer loop with proper polling
  loop do
    begin
      # Poll for messages with timeout
      consumer.each_message(max_wait_time: 2) do |message|
        puts "📱 Received aggregated event from #{message.key}"
        
        success = process_aggregated_event(message, producer)
        
        if success
          puts "✅ Event processed successfully"
        else
          puts "❌ Failed to process event"
        end
      end
    rescue Kafka::ProcessingError => e
      puts "⚠️ Processing error: #{e.message}"
      sleep(1)
    rescue => e
      puts "⚠️ Consumer error: #{e.message}"
      sleep(1)
    end
  end
  
rescue Interrupt
  puts "🛑 Shutting down consumer..."
rescue => e
  puts "❌ Consumer error: #{e.message}"
  puts e.backtrace.first(5)
ensure
  consumer&.close
  producer&.close
  puts "✅ Consumer shutdown complete"
end
