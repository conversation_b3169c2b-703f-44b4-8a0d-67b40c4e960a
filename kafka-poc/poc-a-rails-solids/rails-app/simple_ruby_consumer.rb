#!/usr/bin/env ruby

# Simple Ruby Kafka Consumer using rdkafka
# Consumes from whatsapp_aggregated_events and publishes to mia-requests

require 'bundler/setup'
require 'rdkafka'
require 'json'
require 'time'

puts "🚀 Starting Simple Ruby Kafka Consumer..."

# Check if enabled
if ENV['KARAFKA_ENABLED'] != 'true'
  puts "Consumer disabled (KARAFKA_ENABLED != 'true')"
  exit 0
end

# Consumer configuration
consumer_config = Rdkafka::Config.new(
  'bootstrap.servers' => ENV.fetch('KAFKA_BROKERS', 'kafka:29092'),
  'group.id' => 'simple-ruby-windowing-consumer',
  'auto.offset.reset' => 'latest',
  'enable.auto.commit' => true
)

# Producer configuration
producer_config = Rdkafka::Config.new(
  'bootstrap.servers' => ENV.fetch('KAFKA_BROKERS', 'kafka:29092'),
  'acks' => 'all'
)

puts "🔄 Creating consumer and producer..."

# Create consumer and producer
consumer = consumer_config.consumer
producer = producer_config.producer

puts "✅ Consumer and producer created"

# Subscribe to aggregated events
consumer.subscribe('whatsapp_aggregated_events')
puts "📋 Subscribed to whatsapp_aggregated_events"

def process_aggregated_event(message, producer)
  begin
    # Parse the event data
    event_data = JSON.parse(message.payload)
    user_id = message.key
    
    puts "🔄 Processing aggregated event for #{user_id} (#{event_data['message_count']} messages)"
    
    # Create conversation data for MIA
    conversation_id = "ruby-#{Time.now.to_i}-#{user_id}"
    
    message_data = {
      conversation_id: conversation_id,
      phone: user_id,
      messages: event_data['messages'],
      metadata: {
        strategy: 'simple_ruby_kafka_windowing',
        sent_to_kafka_at: Time.now.strftime('%Y-%m-%dT%H:%M:%SZ'),
        windowing_method: 'kafka_state_store',
        message_count: event_data['message_count']
      }
    }
    
    # Publish to mia-requests
    producer.produce(
      topic: 'mia-requests',
      key: user_id,
      payload: message_data.to_json
    )
    
    puts "📤 Published to mia-requests: #{conversation_id}"
    return true
    
  rescue => e
    puts "❌ Error processing event: #{e.message}"
    puts e.backtrace.first(3)
    return false
  end
end

puts "🔄 Starting consumer loop..."

begin
  # Main consumer loop with infinite polling
  loop do
    consumer.each do |message|
      puts "📱 Received aggregated event from #{message.key}"

      success = process_aggregated_event(message, producer)

      if success
        puts "✅ Event processed successfully"
      else
        puts "❌ Failed to process event"
      end
    end

    puts "🔄 No messages, waiting..."
    sleep(1)
  end
  
rescue Interrupt
  puts "🛑 Shutting down consumer..."
rescue => e
  puts "❌ Consumer error: #{e.message}"
  puts e.backtrace.first(5)
ensure
  consumer&.close
  producer&.close
  puts "✅ Consumer shutdown complete"
end
