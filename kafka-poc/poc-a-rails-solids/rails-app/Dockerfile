FROM ruby:3.2-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    libyaml-dev \
    nodejs \
    npm \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install gems
COPY Gemfile* ./
RUN bundle install

# Copy application
COPY . .

# Create necessary directories
RUN mkdir -p tmp/pids log

EXPOSE 3000

# Default command (will be overridden by docker-compose)
CMD ["rails", "server", "-b", "0.0.0.0"]
