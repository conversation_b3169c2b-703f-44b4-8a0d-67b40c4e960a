#!/usr/bin/env ruby

# Silence Detector - Runs every second to detect windows ready for processing
# This simulates the Mercately windowing behavior

require_relative '../config/environment'

puts "🔍 Starting Silence Detector (Mercately-style windowing)"
puts "⏱️  Checking every 1 second for windows with 10+ seconds of silence"

loop do
  begin
    SilenceDetectorJob.perform_now
  rescue => e
    Rails.logger.error "❌ Silence detector error: #{e.message}"
    puts "❌ Error: #{e.message}"
  end
  
  sleep 1
end
