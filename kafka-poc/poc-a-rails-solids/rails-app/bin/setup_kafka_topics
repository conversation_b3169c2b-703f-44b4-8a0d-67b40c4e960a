#!/usr/bin/env ruby

# Setup Kafka topics for POC B - Karafka windowing evolution
# NOTE: Topics are now automatically created by <PERSON>er Compose (kafka-init service)
# This script is kept for manual setup if needed

require_relative '../config/environment'

puts "🚀 Setting up Kafka topics for POC B windowing..."
puts "ℹ️  NOTE: Topics are automatically created by Docker Compose"
puts "    This script is for manual setup only"

# Topic configurations
topics = {
  'whatsapp-messages' => {
    partitions: 3,
    replication_factor: 1,
    config: {
      'retention.ms' => '86400000' # 24 hours
    }
  },
  'whatsapp-sessions-state' => {
    partitions: 3,
    replication_factor: 1,
    config: {
      'cleanup.policy' => 'compact',
      'segment.ms' => '300000', # 5 minutes
      'delete.retention.ms' => '60000', # 1 minute for tombstones
      'min.cleanable.dirty.ratio' => '0.1'
    }
  },
  'whatsapp-aggregated-events' => {
    partitions: 3,
    replication_factor: 1,
    config: {
      'retention.ms' => '86400000' # 24 hours
    }
  }
}

# Create topics using kafka-topics command
kafka_brokers = ENV.fetch('KAFKA_BROKERS', 'kafka:29092')

topics.each do |topic_name, config|
  puts "📝 Creating topic: #{topic_name}"
  
  # Build kafka-topics command
  cmd = [
    'docker', 'exec', 'kafka',
    'kafka-topics', '--create',
    '--bootstrap-server', kafka_brokers,
    '--topic', topic_name,
    '--partitions', config[:partitions].to_s,
    '--replication-factor', config[:replication_factor].to_s
  ]
  
  # Add configurations
  if config[:config]
    config_string = config[:config].map { |k, v| "#{k}=#{v}" }.join(',')
    cmd += ['--config', config_string]
  end
  
  # Execute command
  result = system(*cmd)
  
  if result
    puts "✅ Topic #{topic_name} created successfully"
  else
    puts "⚠️  Topic #{topic_name} might already exist or creation failed"
  end
end

puts ""
puts "🎯 Kafka topics setup complete!"
puts "📋 Topics created:"
topics.keys.each { |topic| puts "   - #{topic}" }
puts ""
puts "🔄 You can now toggle between windowing strategies:"
puts "   - Traditional: Redis + Sidekiq + MessageWindow"
puts "   - Kafka: Karafka + State Store + Session Windowing"
