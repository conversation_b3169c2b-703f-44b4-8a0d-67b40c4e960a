#!/usr/bin/env ruby

# Ruby Kafka Consumers - Simple alternative to Karafka
# Handles all Kafka consuming with ruby-kafka

require 'bundler/setup'
require 'kafka'
require 'json'
require 'redis'

# Load Rails environment for models and services
puts "🔄 Loading Rails environment..."
ENV['RAILS_ENV'] ||= 'development'

begin
  require 'timeout'
  Timeout::timeout(60) do
    require_relative 'config/environment'
  end
  puts "✅ Rails environment loaded successfully"
rescue Timeout::Error
  puts "❌ Rails loading timed out after 60 seconds"
  exit 1
rescue => e
  puts "❌ Error loading Rails: #{e.message}"
  puts e.backtrace.first(3)
  exit 1
end

# Check if enabled
if ENV['KARAFKA_ENABLED'] != 'true'
  puts "Ruby Kafka Consumers disabled (KARAFKA_ENABLED != 'true')"
  exit 0
end

puts "🚀 Starting Ruby Kafka Consumers..."

# Initialize Kafka client
puts "🔄 Creating Kafka client..."
kafka = Kafka.new(
  seed_brokers: [ENV.fetch('KAFKA_BROKERS', 'kafka:29092')],
  client_id: 'ruby-kafka-consumers'
)
puts "✅ Kafka client created"

# Create consumers for different topics
whatsapp_consumer = kafka.consumer(group_id: 'whatsapp-windowing-group')
aggregated_consumer = kafka.consumer(group_id: 'aggregated-events-group')
mia_response_consumer = kafka.consumer(group_id: 'mia-responses-group')

# Subscribe to topics
whatsapp_consumer.subscribe('whatsapp_messages')
aggregated_consumer.subscribe('whatsapp_aggregated_events')
mia_response_consumer.subscribe('mia_responses')

puts "📋 Subscribed to topics:"
puts "  - whatsapp_messages (windowing)"
puts "  - whatsapp_aggregated_events (MIA processing)"
puts "  - mia_responses (response handling)"
puts "✅ Ruby Kafka Consumers started successfully"

# Consumer functions
def process_whatsapp_message(message)
  begin
    user_id = message.key
    message_data = JSON.parse(message.value)

    puts "📱 Processing WhatsApp message from #{user_id}: #{message_data['content']}"

    # Use SessionWindowManager for windowing logic
    puts "🔄 Getting SessionWindowManager instance..."
    session_manager = SessionWindowManager.instance
    puts "🔄 Calling process_message..."
    session_manager.process_message(user_id, message_data)
    puts "✅ SessionWindowManager.process_message completed"

  rescue => e
    puts "❌ Error processing WhatsApp message: #{e.message}"
    puts "❌ Backtrace: #{e.backtrace.first(3)}"
  end
end

def process_aggregated_event(message)
  user_id = message.key
  event_data = JSON.parse(message.value)
  
  puts "🔄 Processing aggregated event for #{user_id} (#{event_data['message_count']} messages)"
  
  # Create conversation record (same as ProcessMessageWindowJob)
  conversation = create_conversation_from_event(event_data)
  
  puts "🎯 Publishing conversation #{conversation.id} to mia-requests (unified flow)"
  
  # Publish directly to mia-requests (same topic as POC A)
  publish_to_mia_requests(conversation)
  
  puts "✅ Event processed and published to mia-requests successfully"
  
rescue => e
  puts "❌ Error processing aggregated event: #{e.message}"
end

def process_mia_response(message)
  phone = message.key
  response_data = JSON.parse(message.value)
  
  puts "🤖 Processing MIA response for #{phone}"
  
  # Find conversation by correlation_id
  conversation = Conversation.find_by(correlation_id: response_data['conversation_id'])
  
  if conversation
    # Update conversation with response
    conversation.update!(
      mia_response: response_data['response'],
      status: 'completed',
      completed_at: Time.current
    )
    
    # Send to WhatsApp Simulator
    send_to_whatsapp_simulator(conversation, response_data)
    
    puts "✅ MIA response processed for conversation #{conversation.id}"
  else
    puts "⚠️ Conversation not found for correlation_id: #{response_data['conversation_id']}"
  end
  
rescue => e
  puts "❌ Error processing MIA response: #{e.message}"
end

# Helper functions
def create_conversation_from_event(event_data)
  correlation_id = SecureRandom.uuid
  
  conversation_params = {
    phone: event_data['user_id'],
    correlation_id: correlation_id,
    messages: event_data['messages'],
    status: 'processing',
    created_at: Time.current,
    updated_at: Time.current
  }
  
  Conversation.create!(conversation_params)
end

def publish_to_mia_requests(conversation)
  kafka = Kafka.new(
    seed_brokers: [ENV.fetch('KAFKA_BROKERS', 'kafka:29092')],
    client_id: 'aggregated-events-producer'
  )
  
  message_data = {
    conversation_id: conversation.correlation_id,
    phone: conversation.phone,
    messages: conversation.messages.map do |msg|
      {
        content: msg['content'],
        timestamp: msg['timestamp'],
        type: msg['type'] || 'text'
      }
    end,
    metadata: {
      strategy: 'ruby_kafka_windowing',
      sent_to_kafka_at: Time.current.iso8601,
      rails_conversation_id: conversation.id,
      windowing_method: 'ruby_kafka_state_store'
    }
  }
  
  producer = kafka.producer
  producer.produce(
    message_data.to_json,
    topic: 'mia-requests',
    key: conversation.phone
  )
  producer.deliver_messages
  
  puts "📤 Published to mia-requests: #{conversation.correlation_id}"
  
ensure
  producer&.shutdown
  kafka&.close
end

def send_to_whatsapp_simulator(conversation, response_data)
  # Send to WhatsApp Simulator (same as existing logic)
  simulator_url = ENV.fetch('WHATSAPP_SIMULATOR_URL', 'http://whatsapp-simulator:3001')
  
  payload = {
    phone: conversation.phone,
    message: response_data['response'],
    conversation_id: conversation.correlation_id
  }
  
  # Use Net::HTTP or similar to send to simulator
  puts "📱 Sending to WhatsApp Simulator: #{payload[:message][0..50]}..."
end

# Main consumer loop - stable infinite loop
puts "🔄 Starting main consumer loop..."

begin
  # Infinite loop with proper polling
  puts "🔄 Entering stable infinite consumer loop..."
  loop do
    begin
      # Poll for messages with timeout
      whatsapp_consumer.each_message(max_wait_time: 1) do |message|
        puts "📱 Received message, processing..."
        process_whatsapp_message(message)
        puts "✅ Message processed successfully"
      end
    rescue Kafka::ProcessingError => e
      puts "⚠️ Processing error: #{e.message}"
      sleep(1)
    rescue => e
      puts "⚠️ Consumer error: #{e.message}"
      sleep(1)
    end

    puts "🔄 Polling cycle complete, continuing..."
    sleep(0.5)
  end
rescue => e
  puts "❌ Consumer error: #{e.message}"
  puts e.backtrace.first(5)
rescue Interrupt
  puts "🛑 Shutting down Ruby Kafka Consumers..."
ensure
  whatsapp_consumer&.stop
  aggregated_consumer&.stop
  mia_response_consumer&.stop
  kafka&.close
end
