source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "3.2.8"

# Rails 8
gem "rails", "~> 8.0.0"

# Web server
gem "puma", "~> 6.0"

# Database
gem "pg", "~> 1.5"

# Background jobs with Sidekiq
gem "sidekiq", "~> 7.0"

# Redis for Sidekiq and caching
gem "redis", "~> 5.0"

# HTTP client
gem "httparty", "~> 0.21"

# CORS
gem "rack-cors", "~> 2.0"

# JSON
gem "jbuilder", "~> 2.7"

# Kafka (rdkafka is more performant than ruby-kafka)
gem "rdkafka", "~> 0.15"
gem "ruby-kafka", "~> 1.5"

# Karafka for advanced Kafka consumer management
gem "karafka", "~> 2.4"
gem "karafka-web", "~> 0.9"

# Assets
gem "sprockets-rails", ">= 2.0.0"
gem "stimulus-rails"
gem "turbo-rails"

# Development
group :development, :test do
  gem "debug", platforms: %i[ mri mingw x64_mingw ]
  gem "rspec-rails"
  gem "factory_bot_rails"
end

group :development do
  gem "web-console"
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false
