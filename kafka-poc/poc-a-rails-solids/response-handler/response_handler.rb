require 'kafka'
require 'httparty'
require 'json'
require 'logger'

class ResponseHandler
  def initialize
    @logger = Logger.new(STDOUT)
    @logger.level = ENV['LOG_LEVEL'] == 'DEBUG' ? Logger::DEBUG : Logger::INFO
    
    @running = false
    @processed_count = 0
    
    # Rails app URL for sending responses
    @rails_app_url = ENV['RAILS_APP_URL'] || 'http://localhost:3000'
    
    # Kafka consumer for MIA responses
    kafka_brokers = ENV['KAFKA_BROKERS'] || 'localhost:9092'
    @kafka = Kafka.new(kafka_brokers.split(','))
    unique_group = "response_handler_#{Time.now.to_i}"
    @consumer = @kafka.consumer(group_id: unique_group)
    
    # Setup signal handlers
    setup_signal_handlers
  end
  
  def start
    @logger.info "🚀 Starting Response Handler..."
    @running = true
    
    # Subscribe to MIA responses topic (only new messages)
    @consumer.subscribe('mia-responses', start_from_beginning: false)
    
    @consumer.each_message do |message|
      break unless @running
      
      begin
        process_response(message)
        @processed_count += 1
      rescue => e
        @logger.error "❌ Error processing response: #{e.message}"
        @logger.error e.backtrace.join("\n")
      end
    end
  end
  
  def stop
    @logger.info "🛑 Stopping Response Handler..."
    @running = false
    
    @consumer&.stop
    @kafka&.close
    
    @logger.info "✅ Response Handler stopped. Processed #{@processed_count} responses."
  end
  
  private
  
  def process_response(kafka_message)
    phone = kafka_message.key
    response_data = JSON.parse(kafka_message.value)
    
    conversation_id = response_data['conversation_id']
    mia_response = response_data['response']
    
    @logger.info "📤 Received response for #{phone} (#{conversation_id}): #{mia_response[0..50]}..."
    
    # Send response to WhatsApp simulator
    send_to_whatsapp(phone, mia_response, conversation_id, response_data)
  end
  
  def send_to_whatsapp(phone, message, conversation_id, response_data)
    begin
      # Prepare request data
      request_data = {
        phone: phone,
        message: message,
        conversation_id: conversation_id,
        metadata: {
          strategy: 'kafka_async',
          processing_time_ms: response_data['processing_time_ms'],
          processed_by: response_data.dig('metadata', 'processed_by'),
          timestamp: response_data['timestamp']
        }
      }
      
      @logger.info "🔄 Sending response to WhatsApp: #{phone} -> #{message[0..50]}..."

      # Send to WhatsApp simulator
      whatsapp_url = ENV['WHATSAPP_SIMULATOR_URL'] || 'http://whatsapp-simulator:3001'
      response = HTTParty.post(
        "#{whatsapp_url}/receive_response",
        headers: { 'Content-Type' => 'application/json' },
        body: request_data.to_json,
        timeout: 10
      )
      
      if response.success?
        @logger.info "✅ Response sent to WhatsApp successfully for #{phone}"
        @logger.info "📱 WHATSAPP SEND: #{phone} -> #{message}"
        @logger.info "  ↳ Via Kafka strategy (conversation: #{conversation_id})"
      else
        @logger.error "❌ Failed to send to WhatsApp: #{response.code} - #{response.body}"
      end
      
    rescue => e
      @logger.error "❌ Error sending to WhatsApp: #{e.message}"
      raise
    end
  end
  
  def setup_signal_handlers
    Signal.trap('INT') { stop }
    Signal.trap('TERM') { stop }
  end
end

# Run the response handler
if __FILE__ == $0
  handler = ResponseHandler.new
  
  begin
    handler.start
  rescue Interrupt
    puts "\n🛑 Received interrupt signal"
  ensure
    handler.stop
  end
end
