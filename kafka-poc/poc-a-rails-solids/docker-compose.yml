version: '3.8'

services:
  postgres:
    image: postgres:16
    hostname: postgres
    container_name: poc-a-postgres
    environment:
      POSTGRES_DB: mercately_poc
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  rails-app:
    build:
      context: ./rails-app
      dockerfile: Dockerfile
    hostname: rails-app
    container_name: poc-a-rails
    depends_on:
      - postgres
    environment:
      DATABASE_URL: ******************************************/mercately_poc
      RAILS_ENV: development
      RAILS_LOG_TO_STDOUT: "true"
      MIA_FASTAPI_URL: http://mia-fastapi:8000
      WHATSAPP_SIMULATOR_URL: http://whatsapp-simulator:3001
      KAFKA_BROKERS: kafka:29092
      KARAFKA_ENABLED: "false"
      REDIS_URL: redis://redis:6379/0
      BUNDLE_RUBYGEMS__PKG__GITHUB__COM: ${GITHUB_TOKEN}
    ports:
      - "3000:3000"
    volumes:
      - ./rails-app:/app
      - rails_gems:/usr/local/bundle
    restart: unless-stopped
    command: >
      bash -c "
        cd /app &&
        bundle install &&
        rm -f tmp/pids/server.pid &&
        ./bin/rails server -b 0.0.0.0 -p 3000
      "

  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: poc-a-redis
    ports:
      - "6380:6379"
    restart: unless-stopped

  sidekiq-worker:
    build:
      context: ./rails-app
      dockerfile: Dockerfile
    hostname: sidekiq-worker
    container_name: poc-a-sidekiq
    depends_on:
      - postgres
      - redis
      - rails-app
    environment:
      DATABASE_URL: ******************************************/mercately_poc
      REDIS_URL: redis://redis:6379/0
      RAILS_ENV: development
      MIA_FASTAPI_URL: http://mia-fastapi:8000
      WHATSAPP_SIMULATOR_URL: http://whatsapp-simulator:3001
      SIDEKIQ_WORKER: "true"
      KAFKA_BROKERS: kafka:29092
      KARAFKA_ENABLED: "false"
    volumes:
      - ./rails-app:/app
      - rails_gems:/usr/local/bundle
    command: >
      bash -c "
        cd /app &&
        bundle install &&
        echo 'Starting Sidekiq worker and silence detector...' &&
        bundle exec sidekiq &
        echo 'Sidekiq started in background' &&
        sleep 2 &&
        echo 'Starting silence detector...' &&
        ./bin/silence_detector
      "
    restart: unless-stopped

  # Karafka consumers for POC B windowing
  karafka-consumers:
    build:
      context: ./rails-app
      dockerfile: Dockerfile
    hostname: karafka-consumers
    container_name: poc-a-karafka
    depends_on:
      - postgres
      - kafka
      - kafka-init
    environment:
      DATABASE_URL: ******************************************/mercately_poc
      RAILS_ENV: development
      KAFKA_BROKERS: kafka:29092
      KARAFKA_ENABLED: "true"  # Reusing this env var for ruby-kafka consumers
      MIA_FASTAPI_URL: http://mia-fastapi:8000
      WHATSAPP_SIMULATOR_URL: http://whatsapp-simulator:3001
      BUNDLE_RUBYGEMS__PKG__GITHUB__COM: ${GITHUB_TOKEN}
    volumes:
      - ./rails-app:/app
      - rails_gems:/usr/local/bundle
    command: >
      bash -c "
        cd /app &&
        bundle install &&
        echo '🚀 Starting Simple Windowing Consumer (No Rails)...' &&
        bundle exec ruby simple_windowing_consumer.rb
      "
    restart: unless-stopped

  mia-fastapi:
    build:
      context: ./mia-fastapi
      dockerfile: Dockerfile
    hostname: mia-fastapi
    container_name: poc-a-mia
    environment:
      LOG_LEVEL: INFO
    ports:
      - "8000:8000"
    volumes:
      - ./mia-fastapi:/app
    restart: unless-stopped

  # MIA Kafka Consumer (Python) - Processes messages from Kafka
  mia-consumer:
    build:
      context: ./mia-consumer
      dockerfile: Dockerfile
    container_name: poc-a-mia-consumer
    depends_on:
      - kafka
      - mia-fastapi
    environment:
      KAFKA_BROKERS: kafka:29092
      MIA_FASTAPI_URL: http://mia-fastapi:8000
      LOG_LEVEL: INFO
    ports:
      - "8001:8000"
    volumes:
      - ./mia-consumer:/app
    restart: unless-stopped
    networks:
      - default

  whatsapp-simulator:
    build:
      context: ../shared/whatsapp-simulator
      dockerfile: Dockerfile
    hostname: whatsapp-simulator
    container_name: poc-a-simulator
    environment:
      RAILS_APP_URL: http://rails-app:3000
      LOG_LEVEL: INFO
    ports:
      - "3001:3001"
    volumes:
      - ../shared/whatsapp-simulator:/app
    restart: unless-stopped

  # Response Handler (Ruby) - Processes responses from Kafka and sends to WhatsApp
  response-handler:
    build:
      context: ./response-handler
      dockerfile: Dockerfile
    container_name: poc-a-response-handler
    depends_on:
      - kafka
      - rails-app
    environment:
      KAFKA_BROKERS: kafka:29092
      WHATSAPP_SIMULATOR_URL: http://whatsapp-simulator:3001
      RAILS_APP_URL: http://rails-app:3000
    volumes:
      - ./response-handler:/app
    restart: unless-stopped
    networks:
      - default

  adminer:
    image: adminer
    hostname: adminer
    container_name: poc-a-adminer
    depends_on:
      - postgres
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres

  # Kafka Infrastructure (KRaft mode - no Zookeeper needed)
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: poc-a-kafka
    ports:
      - "9092:9092"
    environment:
      KAFKA_NODE_ID: 1
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT'
      KAFKA_ADVERTISED_LISTENERS: 'PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092'
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_PROCESS_ROLES: 'broker,controller'
      KAFKA_CONTROLLER_QUORUM_VOTERS: '1@kafka:29093'
      KAFKA_LISTENERS: 'PLAINTEXT://kafka:29092,CONTROLLER://kafka:29093,PLAINTEXT_HOST://0.0.0.0:9092'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'PLAINTEXT'
      KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
      KAFKA_LOG_DIRS: '/tmp/kraft-combined-logs'
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      # KRaft required
      CLUSTER_ID: 'MkU3OEVBNTcwNTJENDM2Qk'
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "kafka:29092", "--list"]
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - default

  # Kafka topic initialization
  kafka-init:
    image: confluentinc/cp-kafka:7.4.0
    container_name: poc-a-kafka-init
    depends_on:
      - kafka
    command: |
      bash -c "
        echo '🚀 Waiting for Kafka to be ready...'
        until kafka-topics --bootstrap-server kafka:29092 --list > /dev/null 2>&1; do
          echo 'Waiting for Kafka...'
          sleep 2
        done

        echo '📝 Creating POC A topics (existing)...'
        kafka-topics --bootstrap-server kafka:29092 --create --if-not-exists --topic mia-requests --partitions 3 --replication-factor 1
        kafka-topics --bootstrap-server kafka:29092 --create --if-not-exists --topic mia-responses --partitions 3 --replication-factor 1

        echo '🔄 Creating POC B topics (Karafka windowing)...'

        # WhatsApp messages topic (incoming messages)
        kafka-topics --bootstrap-server kafka:29092 --create --if-not-exists --topic whatsapp-messages --partitions 3 --replication-factor 1 --config retention.ms=86400000

        # Session state topic (compacted for persistence)
        kafka-topics --bootstrap-server kafka:29092 --create --if-not-exists --topic whatsapp-sessions-state --partitions 3 --replication-factor 1 --config cleanup.policy=compact --config segment.ms=300000 --config delete.retention.ms=60000 --config min.cleanable.dirty.ratio=0.1

        # Aggregated events topic (windowed messages for MIA)
        kafka-topics --bootstrap-server kafka:29092 --create --if-not-exists --topic whatsapp-aggregated-events --partitions 3 --replication-factor 1 --config retention.ms=86400000

        echo '✅ All Kafka topics created successfully!'
        echo '📋 Available topics:'
        kafka-topics --bootstrap-server kafka:29092 --list

        echo ''
        echo '🎯 POC Evolution Ready:'
        echo '   - POC A: Traditional windowing (Redis/Sidekiq)'
        echo '   - POC B: Kafka windowing (Karafka/State Store)'
        echo '   - Toggle via API: /api/windowing_strategy'
      "
    networks:
      - default

  # Kafka UI for web management
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: poc-a-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8081:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: poc-a-cluster
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    networks:
      - default

volumes:
  postgres_data:
  rails_gems:

networks:
  default:
    name: poc-a-network
