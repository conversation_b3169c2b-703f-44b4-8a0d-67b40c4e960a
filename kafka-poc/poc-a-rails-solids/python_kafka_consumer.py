#!/usr/bin/env python3

"""
Simple Python Kafka Consumer for Windowing
Consumes from whatsapp_aggregated_events and publishes to mia-requests
"""

import json
import os
import time
from kafka import KafkaConsumer, KafkaProducer
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_consumer():
    """Create Kafka consumer for aggregated events"""
    return KafkaConsumer(
        'whatsapp_aggregated_events',
        bootstrap_servers=[os.getenv('KAFKA_BROKERS', 'kafka:29092')],
        group_id='python-windowing-consumer',
        auto_offset_reset='earliest',
        enable_auto_commit=True,
        value_deserializer=lambda x: json.loads(x.decode('utf-8'))
    )

def create_producer():
    """Create Kafka producer for mia-requests"""
    return KafkaProducer(
        bootstrap_servers=[os.getenv('KAFKA_BROKERS', 'kafka:29092')],
        value_serializer=lambda x: json.dumps(x).encode('utf-8')
    )

def process_aggregated_event(event_data, producer):
    """Process aggregated event and publish to mia-requests"""
    try:
        logger.info(f"🔄 Processing aggregated event for {event_data['user_id']} ({event_data['message_count']} messages)")
        
        # Create conversation data for MIA
        conversation_id = f"kafka-{int(time.time())}-{event_data['user_id']}"
        
        message_data = {
            'conversation_id': conversation_id,
            'phone': event_data['user_id'],
            'messages': event_data['messages'],
            'metadata': {
                'strategy': 'python_kafka_windowing',
                'sent_to_kafka_at': time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                'windowing_method': 'kafka_state_store',
                'message_count': event_data['message_count']
            }
        }
        
        # Publish to mia-requests
        producer.send(
            'mia-requests',
            key=event_data['user_id'].encode('utf-8'),
            value=message_data
        )
        producer.flush()
        
        logger.info(f"📤 Published to mia-requests: {conversation_id}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error processing event: {e}")
        return False

def main():
    """Main consumer loop"""
    logger.info("🚀 Starting Python Kafka Consumer for Windowing...")
    
    # Create consumer and producer
    consumer = create_consumer()
    producer = create_producer()
    
    logger.info("✅ Consumer and producer created successfully")
    logger.info("🔄 Starting to consume aggregated events...")
    
    try:
        for message in consumer:
            try:
                event_data = message.value
                logger.info(f"📱 Received aggregated event: {event_data.get('user_id', 'unknown')}")
                
                # Process the event
                success = process_aggregated_event(event_data, producer)
                
                if success:
                    logger.info("✅ Event processed successfully")
                else:
                    logger.error("❌ Failed to process event")
                    
            except Exception as e:
                logger.error(f"❌ Error processing message: {e}")
                continue
                
    except KeyboardInterrupt:
        logger.info("🛑 Shutting down consumer...")
    except Exception as e:
        logger.error(f"❌ Consumer error: {e}")
    finally:
        consumer.close()
        producer.close()
        logger.info("✅ Consumer shutdown complete")

if __name__ == "__main__":
    main()
