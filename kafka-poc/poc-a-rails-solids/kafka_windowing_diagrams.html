<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windowing con Kafka States - Diagrama<PERSON> Completos</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 40px;
            font-style: italic;
        }
        h2 {
            color: #34495e;
            border-left: 5px solid #3498db;
            padding-left: 20px;
            margin-top: 50px;
            font-size: 1.8em;
        }
        .diagram-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
        }
        .diagram-title {
            color: #495057;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            background: linear-gradient(90deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .diagram-description {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .status-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .status-working {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        .status-loading {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .metric {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
            border: 2px solid #dee2e6;
        }
        .toc h3 {
            color: #495057;
            margin-top: 0;
        }
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        .toc li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .toc a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .toc a:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        .highlight-box {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ff6b6b;
        }
        .mermaid {
            text-align: center;
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Windowing con Kafka States</h1>
        <p class="subtitle">Diagramas Completos de la Arquitectura de Windowing usando Kafka como State Store</p>

        <div class="highlight-box">
            <h3>🎯 Estado Actual del Sistema</h3>
            <div class="status-grid">
                <div class="status-card status-working">
                    <div class="metric">✅ 10</div>
                    <div>Eventos Agregados Generados</div>
                </div>
                <div class="status-card status-working">
                    <div class="metric">✅ 100%</div>
                    <div>Windowing Funcionando</div>
                </div>
                <div class="status-card status-loading">
                    <div class="metric">⏳ 85%</div>
                    <div>Consumer Cargando Rails</div>
                </div>
            </div>
        </div>

        <div class="toc">
            <h3>📋 Índice de Diagramas</h3>
            <ul>
                <li><a href="#flujo-principal">1. Flujo Principal del Windowing</a></li>
                <li><a href="#secuencia-detallada">2. Secuencia Detallada del SessionWindowManager</a></li>
                <li><a href="#arquitectura-topics">3. Arquitectura de Kafka Topics</a></li>
                <li><a href="#comparacion">4. Comparación: Estándar vs Kafka States</a></li>
                <li><a href="#componentes">5. Componentes y Tecnologías</a></li>
            </ul>
        </div>

        <!-- Diagrama 1: Flujo Principal -->
        <h2 id="flujo-principal">1. 🔄 Flujo Principal del Windowing</h2>
        <div class="diagram-container">
            <div class="diagram-title">Arquitectura Completa del Windowing con Kafka States</div>
            <div class="diagram-description">
                <strong>Descripción:</strong> Este diagrama muestra el flujo completo desde que llega un mensaje de WhatsApp hasta que se envía la respuesta final, utilizando Kafka como state store para el windowing.
            </div>
            <div class="mermaid">
graph TD
    A[📱 WhatsApp Message] --> B[Rails Webhook]
    B --> C{Strategy?}
    C -->|kafka_windowing=true| D[SessionWindowManager]
    C -->|standard| E[Redis + Sidekiq]

    D --> F[Kafka State Store<br/>whatsapp-sessions-state]
    F --> G[Session Window Logic<br/>10 seconds silence]
    G --> H[Aggregated Event]
    H --> I[📤 Publish to<br/>whatsapp_aggregated_events]

    I --> J[Ruby Kafka Consumer]
    J --> K[Create Conversation]
    K --> L[📤 Publish to<br/>mia-requests]

    L --> M[🐍 Python MIA Consumer]
    M --> N[🤖 MIA Processing]
    N --> O[📤 Publish to<br/>mia_responses]

    O --> P[Ruby Response Consumer]
    P --> Q[📱 WhatsApp Simulator]

    style D fill:#e1f5fe
    style F fill:#f3e5f5
    style I fill:#e8f5e8
    style L fill:#fff3e0
    style O fill:#fce4ec
            </div>
        </div>

        <!-- Diagrama 2: Secuencia Detallada -->
        <h2 id="secuencia-detallada">2. ⏱️ Secuencia Detallada del SessionWindowManager</h2>
        <div class="diagram-container">
            <div class="diagram-title">Lógica de Windowing con Timer de 10 Segundos</div>
            <div class="diagram-description">
                <strong>Descripción:</strong> Secuencia temporal que muestra cómo funciona el timer de windowing, el reset del timer con nuevos mensajes, y la generación de eventos agregados.
            </div>
            <div class="mermaid">
sequenceDiagram
    participant WA as 📱 WhatsApp
    participant R as Rails App
    participant SWM as SessionWindowManager
    participant KSS as Kafka State Store
    participant KP as Kafka Producer
    participant KC as Kafka Consumer

    WA->>R: Message 1
    R->>SWM: process_message(user_id, msg1)
    SWM->>KSS: Store message in session
    SWM->>SWM: Schedule window close (10s)

    Note over SWM: Timer: 10 seconds

    WA->>R: Message 2 (within 10s)
    R->>SWM: process_message(user_id, msg2)
    SWM->>KSS: Add to existing session
    SWM->>SWM: Reset timer (10s)

    Note over SWM: Timer: Reset to 10 seconds

    Note over SWM: 10 seconds of silence...

    SWM->>KSS: Retrieve all messages in window
    SWM->>SWM: Create aggregated event
    SWM->>KP: Publish to whatsapp_aggregated_events
    SWM->>KSS: Clear session state

    KC->>KC: Consume aggregated event
    KC->>KC: Create Conversation record
    KC->>KP: Publish to mia-requests

    Note over SWM,KC: Windowing Complete!
            </div>
        </div>

        <!-- Diagrama 3: Arquitectura de Topics -->
        <h2 id="arquitectura-topics">3. 🗄️ Arquitectura de Kafka Topics</h2>
        <div class="diagram-container">
            <div class="diagram-title">Flujo de Datos a través de los Topics de Kafka</div>
            <div class="diagram-description">
                <strong>Descripción:</strong> Arquitectura de topics mostrando cómo fluyen los datos desde mensajes individuales hasta respuestas finales, con cada layer cumpliendo una función específica.
            </div>
            <div class="mermaid">
graph LR
    subgraph "Input Layer"
        WM[📱 whatsapp_messages<br/>Raw messages]
    end

    subgraph "State Management"
        WSS[🗄️ whatsapp-sessions-state<br/>Session windows state<br/>Key: user_id<br/>Value: session_data]
    end

    subgraph "Processing Layer"
        WAE[📦 whatsapp_aggregated_events<br/>Windowed message groups<br/>Key: user_id<br/>Value: aggregated_event]
    end

    subgraph "MIA Layer"
        MR[🤖 mia-requests<br/>Conversations for MIA<br/>Key: phone<br/>Value: conversation_data]

        MRES[💬 mia_responses<br/>MIA responses<br/>Key: phone<br/>Value: response_data]
    end

    subgraph "Output Layer"
        WS[📱 WhatsApp Simulator<br/>Final responses]
    end

    WM --> WSS
    WSS --> WAE
    WAE --> MR
    MR --> MRES
    MRES --> WS

    style WSS fill:#f3e5f5
    style WAE fill:#e8f5e8
    style MR fill:#fff3e0
    style MRES fill:#fce4ec
            </div>
        </div>

        <!-- Diagrama 4: Comparación -->
        <h2 id="comparacion">4. ⚖️ Comparación: Estándar vs Kafka States</h2>
        <div class="diagram-container">
            <div class="diagram-title">Windowing Estándar (Redis + Sidekiq) vs Windowing con Kafka States</div>
            <div class="diagram-description">
                <strong>Descripción:</strong> Comparación lado a lado de ambos enfoques, mostrando las diferencias en arquitectura, componentes y flujo de datos.
            </div>
            <div class="mermaid">
graph TB
    subgraph "Windowing Estándar (Redis + Sidekiq)"
        A1[📱 WhatsApp Message] --> B1[Rails Webhook]
        B1 --> C1[Redis State Store]
        C1 --> D1[Sidekiq Job Scheduler]
        D1 --> E1[ProcessMessageWindowJob]
        E1 --> F1[HTTP Request to MIA]
        F1 --> G1[📱 WhatsApp Response]
    end

    subgraph "Windowing con Kafka States"
        A2[📱 WhatsApp Message] --> B2[Rails Webhook]
        B2 --> C2[SessionWindowManager]
        C2 --> D2[Kafka State Store]
        D2 --> E2[whatsapp_aggregated_events]
        E2 --> F2[Ruby Kafka Consumer]
        F2 --> G2[mia-requests Topic]
        G2 --> H2[Python MIA Consumer]
        H2 --> I2[mia_responses Topic]
        I2 --> J2[Ruby Response Consumer]
        J2 --> K2[📱 WhatsApp Response]
    end

    style C1 fill:#ffebee
    style D1 fill:#ffebee
    style C2 fill:#e8f5e8
    style D2 fill:#e8f5e8
    style E2 fill:#e8f5e8
            </div>
        </div>

        <!-- Diagrama 5: Componentes y Tecnologías -->
        <h2 id="componentes">5. 🛠️ Componentes y Tecnologías</h2>
        <div class="diagram-container">
            <div class="diagram-title">Stack Tecnológico del Windowing con Kafka</div>
            <div class="diagram-description">
                <strong>Descripción:</strong> Detalle de todos los componentes, tecnologías y librerías utilizadas en la implementación del windowing con Kafka states.
            </div>
            <div class="mermaid">
graph TD
    subgraph "Frontend Layer"
        WS[📱 WhatsApp Simulator<br/>React + Node.js]
    end

    subgraph "Application Layer"
        RA[🚀 Rails App<br/>Ruby on Rails 7<br/>Puma Server]
        SWM[⚙️ SessionWindowManager<br/>Ruby Singleton<br/>Windowing Logic]
    end

    subgraph "Message Processing"
        RKC[💎 Ruby Kafka Consumer<br/>ruby-kafka gem<br/>Rails environment]
        PMC[🐍 Python MIA Consumer<br/>FastAPI + aiokafka<br/>Async processing]
    end

    subgraph "Kafka Infrastructure"
        KB[🔗 Kafka Brokers<br/>Apache Kafka 2.8<br/>Docker containers]

        subgraph "Topics"
            T1[whatsapp_messages]
            T2[whatsapp-sessions-state]
            T3[whatsapp_aggregated_events]
            T4[mia-requests]
            T5[mia_responses]
        end
    end

    subgraph "Data Layer"
        PG[🐘 PostgreSQL<br/>Conversations<br/>User sessions]
        KS[🗄️ Kafka State Store<br/>Distributed state<br/>Message windows]
    end

    RA --> SWM
    SWM --> KB
    KB --> T1
    KB --> T2
    KB --> T3
    KB --> T4
    KB --> T5

    T3 --> RKC
    T4 --> PMC
    T5 --> RKC

    RKC --> PG
    RKC --> WS

    style SWM fill:#e1f5fe
    style KB fill:#f3e5f5
    style KS fill:#f3e5f5
    style RKC fill:#e8f5e8
    style PMC fill:#fff3e0
            </div>
        </div>

        <!-- Información adicional -->
        <div class="highlight-box">
            <h3>📊 Métricas y Estado Actual</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5em; color: #27ae60; font-weight: bold;">✅ 10</div>
                    <div style="color: #7f8c8d;">Eventos Agregados</div>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5em; color: #27ae60; font-weight: bold;">✅ 100%</div>
                    <div style="color: #7f8c8d;">Windowing Activo</div>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5em; color: #f39c12; font-weight: bold;">⏳ 85%</div>
                    <div style="color: #7f8c8d;">Consumer Loading</div>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5em; color: #27ae60; font-weight: bold;">✅ UP</div>
                    <div style="color: #7f8c8d;">Python MIA</div>
                </div>
            </div>
        </div>

        <div style="background: #2c3e50; color: white; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;">
            <h3 style="color: white; margin-top: 0;">🎯 Resultado Final</h3>
            <p style="font-size: 1.2em; margin-bottom: 0;">
                <strong>¡Windowing con Kafka States FUNCIONANDO!</strong><br>
                Alternativa exitosa al windowing estándar (Redis + Sidekiq)
            </p>
        </div>

        <script>
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true
                },
                sequence: {
                    useMaxWidth: true,
                    wrap: true
                }
            });
        </script>
    </div>
</body>
</html>