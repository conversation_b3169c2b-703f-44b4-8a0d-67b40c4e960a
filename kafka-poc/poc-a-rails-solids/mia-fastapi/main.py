import asyncio
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="MIA FastAPI - POC A",
    description="MIA Processing Engine for Rails 8 + Solids POC",
    version="1.0.0"
)

# Pydantic models
class MessageData(BaseModel):
    content: str
    timestamp: str
    type: str = "text"
    message_id: Optional[str] = None

class ConversationRequest(BaseModel):
    correlation_id: str
    phone: str
    conversation_id: str
    messages: List[MessageData]
    metadata: Optional[Dict] = None

class ConversationResponse(BaseModel):
    correlation_id: str
    response: str
    processing_time_ms: int
    timestamp: str
    metadata: Optional[Dict] = None

class MIAEngine:
    """
    MIA Processing Engine - Simulates AI conversation processing
    In a real implementation, this would connect to your ML models
    """
    
    def __init__(self):
        self.processing_count = 0
    
    async def process_conversation(self, conversation: ConversationRequest) -> str:
        """Process a conversation and generate a response"""
        start_time = time.time()
        
        self.processing_count += 1
        
        # Extract message contents
        message_texts = [msg.content for msg in conversation.messages]
        full_conversation = "\n".join(message_texts)
        
        logger.info(f"🤖 Processing conversation {conversation.correlation_id}")
        logger.info(f"📱 Phone: {conversation.phone}")
        logger.info(f"💬 Messages: {len(message_texts)}")
        
        # Simulate processing time (0.5-2 seconds)
        processing_time = 0.5 + (len(message_texts) * 0.2)
        await asyncio.sleep(processing_time)
        
        # Generate response based on conversation content
        response = self._generate_response(message_texts, conversation.phone)
        
        end_time = time.time()
        duration_ms = int((end_time - start_time) * 1000)
        
        logger.info(f"✅ Generated response in {duration_ms}ms: {response[:100]}...")
        
        return response
    
    def _generate_response(self, messages: List[str], phone: str) -> str:
        """Generate a contextual response based on messages"""

        # Simple keyword-based responses (simulate AI)
        last_message = messages[-1].lower() if messages else ""
        all_messages = " ".join(messages).lower()

        # Create message summary for windowing demonstration
        if len(messages) > 1:
            message_summary = " + ".join([f'"{msg}"' for msg in messages])
            windowing_info = f"\n\n📝 Mensajes procesados en esta ventana: {message_summary}"
        else:
            windowing_info = ""

        # Greeting patterns
        if any(word in last_message for word in ["hola", "hello", "hi", "buenos días", "buenas tardes"]):
            return f"¡Hola! 👋 Soy tu asistente virtual. He recibido {len(messages)} mensaje(s).{windowing_info} ¿En qué puedo ayudarte hoy?"

        # Help/assistance patterns
        elif any(word in all_messages for word in ["ayuda", "help", "problema", "issue", "support"]):
            return f"Entiendo que necesitas ayuda. Te puedo asistir con información, consultas o resolver dudas.{windowing_info} ¿Podrías contarme más detalles sobre lo que necesitas?"

        # Questions patterns
        elif any(word in last_message for word in ["?", "cómo", "cuándo", "dónde", "qué", "por qué"]):
            return f"Es una buena pregunta. Basándome en tu consulta de {len(messages)} mensaje(s), te voy a ayudar con eso.{windowing_info} ¿Podrías darme un poco más de contexto?"

        # Default response
        else:
            return f"He recibido tu mensaje con {len(messages)} parte(s). Como tu asistente virtual, estoy aquí para ayudarte.{windowing_info} ¿Podrías contarme un poco más sobre lo que necesitas para poder darte la mejor respuesta?"

# Global MIA engine instance
mia_engine = MIAEngine()

@app.get("/")
async def root():
    return {
        "service": "MIA FastAPI POC A",
        "status": "running",
        "version": "1.0.0",
        "architecture": "Rails 8 + Solids"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "processed_conversations": mia_engine.processing_count
    }

@app.post("/process_conversation", response_model=ConversationResponse)
async def process_conversation(conversation: ConversationRequest):
    """Process a conversation and return AI response"""
    try:
        start_time = time.time()
        
        logger.info(f"📨 Received conversation {conversation.correlation_id} from {conversation.phone}")
        
        # Validate input
        if not conversation.messages:
            raise HTTPException(status_code=400, detail="No messages provided")
        
        # Process with MIA engine
        response_text = await mia_engine.process_conversation(conversation)
        
        # Calculate processing time
        end_time = time.time()
        processing_time_ms = int((end_time - start_time) * 1000)
        
        # Create response
        response = ConversationResponse(
            correlation_id=conversation.correlation_id,
            response=response_text,
            processing_time_ms=processing_time_ms,
            timestamp=datetime.utcnow().isoformat(),
            metadata={
                "phone": conversation.phone,
                "message_count": len(conversation.messages),
                "conversation_id": conversation.conversation_id,
                "processed_by": "MIA_ENGINE_POC_A"
            }
        )
        
        logger.info(f"✅ Conversation {conversation.correlation_id} processed successfully")
        
        return response
        
    except Exception as e:
        logger.error(f"❌ Error processing conversation {conversation.correlation_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.get("/stats")
async def get_stats():
    """Get processing statistics"""
    return {
        "total_processed": mia_engine.processing_count,
        "uptime": "running",
        "architecture": "Rails 8 + Solid Queue/Cache",
        "timestamp": datetime.utcnow().isoformat()
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
